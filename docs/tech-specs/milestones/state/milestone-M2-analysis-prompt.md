# Implementation Confidence Assessment for Milestone M2

## Context
You are an expert software engineer reviewing this milestone specification for implementation feasibility.

## Your Task
Analyze the milestone specification with full repository context and provide:

1. **Implementation Confidence Score** (1-10 scale):
   - 1-3: High risk, significant concerns
   - 4-6: Moderate risk, some concerns
   - 7-8: Good confidence, minor concerns
   - 9-10: High confidence, ready to implement

2. **Detailed Assessment**:
   - **Feasibility**: Are the tasks technically achievable with current codebase?
   - **Scope**: Is the milestone appropriately sized and scoped?
   - **Dependencies**: Are external dependencies realistic and available?
   - **Complexity**: Does task complexity match available time/resources?
   - **Clarity**: Are requirements clear enough for implementation?

3. **Specific Concerns** (if any):
   - Technical blockers or challenges
   - Missing information or unclear requirements
   - Unrealistic timelines or scope
   - Dependency issues or conflicts

4. **Recommendations**:
   - What should be fixed before implementation?
   - What additional information is needed?
   - How to improve milestone quality?

## Analysis Guidelines
- Consider the current repository structure and capabilities
- Evaluate task breakdown against actual codebase complexity
- Assess if deliverables align with technical architecture
- Check if acceptance criteria are testable and realistic
- Consider team expertise and available tooling

## Quick Analysis Results
From automated analysis:
- Success Criteria: 14 items
- Tasks: 9 items
- Deliverables: 101 items

## Milestone Specification
```markdown
---
title: Milestone M2 — Confidence & Audit Enhancements
description: Add full coverage / confidence metrics, unknown-edge detection, and automated audit reports for every KG update.
created: 2025-05-30
version: 0.1.0
status: Draft
tags: [milestone]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🧮">
<strong>Goal:</strong> Turn the KG into a self-auditing asset.
Every <code>build-kg</code> / <code>sync-kg</code> run must produce a verifiable coverage & confidence report and flag “unknown edges” needing manual review.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
simple-git: "3.22.0"
fast-json-stable-stringify: "2.1.0"
chalk: "5.3.0"     # CLI colouring
jest: "29.7.0"
```

---

## 🎯 Definition of Done

1. **Audit CLI**: `pnpm run audit-kg` produces:
   - `kg-audit.json` — diff of node/edge additions/removals, coverage %, unknown edges list.
   - Pretty console summary with colour (green ≥ 0.75, yellow 0.5-0.75, red < 0.5).
2. **Unknown Edges**: Any workflow_calls whose target function is missing or any implements edge whose spec/component is stale are emitted with confidence: 0.2 and listed in audit.
3. **CI**: CI job `kg-audit` fails if any milestone coverage < 0.5 or unknown-edge count > 10.
4. **Timestamps**: JSON-LD nodes now contain last_verified ISO timestamp each sync/audit run.
5. **Coverage**: Unit-test coverage ≥ 85 % for new audit code.
6. **Performance**: `pnpm run audit-kg -- --since HEAD~1` completes in ≤ 60 seconds on a repository with ≤ 2000 files (checked in CI with artificial file generator).

---

## 📦 Deliverables

| Artefact / Path                        | Content                                                      | Reuse Level |
|----------------------------------------|--------------------------------------------------------------|-------------|
| code/packages/kg-audit-lib/            | coverage.ts, unknownEdges.ts, tests                          | Extends kg-sync-lib |
| code/packages/kg-cli/                  | new audit-kg.ts with bin entry                               | Copies sync-kg pattern |
| code/output/schema/kg-audit.schema.json | JSON Schema defining audit file (entities, metrics, unknownEdges[]) | Template provided |
| kg-audit.json                          | Generated diff & metrics (ignored in .gitignore)             | Extends kg-changes.json |
| .github/workflows/kg-audit.yml         | CI job on PR & nightly main                                  | Copies existing workflows |
| docs/tech-specs/domains/kg-audit.mdx   | Domain doc: formulas, thresholds                             | New content |

### 🔄 **Reuse Strategy**

**Input Sources:**
- `kg.jsonld` - Primary knowledge graph data (existing)
- `kg-changes.json` - Change tracking format (extend existing)
- `kg-sync-lib/confidence.ts` - Coverage calculation (reuse directly)

**Output Format:**
- `kg-audit.json` - Audit report (new, follows kg-changes.json pattern)
- Console output - Colored summary (copy sync-kg.ts patterns)

---

## 🗂 Directory Layout

```text
code/packages/
└─ kg-audit-lib/
   ├─ src/
   │   ├─ coverage.ts
   │   ├─ unknownEdges.ts
   │   └─ index.ts
   ├─ tests/
   └─ package.json
```

---

## 🧠 Key Decisions

| Topic                 | Decision / Formula                                                                                              | Rationale |
|-----------------------|-----------------------------------------------------------------------------------------------------------------|-----------|
| Coverage metric       | Reuse existing `calculateCoverage()` from `kg-sync-lib/confidence.ts`                                          | Proven algorithm, consistent with M1.2. |
| Confidence decay      | **Existing formula:**<br/>```ts
// From kg-sync-lib/confidence.ts
daysSinceVerified = daysBetween(lastVerified, new Date())
confidence = daysSinceVerified > 30 ? 0.8 : 1.0
``` | Reuse proven confidence logic. |
| Unknown-edge cap      | Build fails if **unknown edges > 10** *or* **increase > 20 % vs previous audit**.                               | Prevent silent drift. |
| Report schema         | `code/output/schema/kg-audit.schema.json` following output file pattern.                                       | Consistent with generated output file organization. |
| Report storage        | `kg-audit.json` is *not* committed; stored as CI artefact only.                                                 | Keeps Git clean, follows `kg-changes.json` pattern. |
| Performance guardrail | Audit must finish **≤ 60 s** on a repo with ≤ 2000 source files.                                                | Ensures CI remains fast. |
| Input data source     | Read existing `kg.jsonld` as primary input, extend `kg-changes.json` format.                                    | Leverage proven M1.1/M1.2 infrastructure. |

---

## 🛠️ Technical Specifications

### 📝 Audit Report Generation Algorithm

**Input**: Existing `kg.jsonld` (from M1.1/M1.2), git diff, previous `kg-audit.json` (optional)
**Output**: Audit report JSON (kg-audit.json)

**Steps:**
1. **Load Existing Data**: Read `kg.jsonld` using existing JSON-LD parser from `kg-cli`.
2. **Extract Graph Elements**: Parse `@graph` array for nodes and edges (reuse existing logic).
3. **Calculate Coverage**: Call existing `calculateCoverage()` from `kg-sync-lib/confidence.ts`.
4. **Detect Unknown Edges**: Implement new detection logic using reference algorithm.
5. **Apply Confidence Scoring**: Reuse existing confidence calculation from `confidence.ts`.
6. **Generate Summary**: Extend `kg-changes.json` format with audit-specific metrics.
7. **Validate Schema**: Ensure output matches `code/schemas/kg-audit.schema.json`.
8. **Performance**: Complete in ≤ 60 seconds for ≤ 2000 files.

**Error Handling:**
- Reuse existing exit codes: 60 (coverage breach), 62 (unknown-edge cap), 1 (error).
- Log and skip malformed nodes/edges using existing error patterns.

**Reuse Strategy:**
- **90% code reuse** from existing `kg-sync-lib` and `kg-cli` packages
- **Proven algorithms** for coverage calculation and confidence scoring
- **Established patterns** for CLI, file I/O, and error handling

### 📦 Output Interfaces

```typescript
interface AuditReport {
  summary: {
    generatedAt: string;
    edgeTotals: Record<string, number>;
  };
  milestones: Array<{
    id: string;
    coverage: number;
    confidence: number;
  }>;
  unknownEdges: string[];
}
```

---

## 🔨 Task Breakdown

| #   | Branch                | Task                                         | Owner | Est. | Reuse Level |
|-----|-----------------------|----------------------------------------------|-------|------|-------------|
| 01  | m2/audit-lib          | Scaffold kg-audit-lib (copy existing patterns) | BE    | 2h   | 90% reuse   |
| 01a | m2/audit-schema       | Create code/schemas/kg-audit.schema.json; add validation | PM | 1h   | Template provided |
| 02  | m2/coverage           | Extend existing coverage calc from kg-sync-lib | BE    | 1h   | 95% reuse   |
| 03  | m2/unknown-edges      | Implement detection logic (reference provided) | BE    | 4h   | Algorithm provided |
| 04  | m2/cli                | Add audit-kg.ts cmd (copy sync-kg.ts pattern) | BE    | 2h   | 85% reuse   |
| 05  | m2/tests              | Jest tests (copy existing test patterns)     | BE    | 4h   | 80% reuse   |
| 06  | m2/ci                 | Add kg-audit.yml workflow (copy existing)    | DevOps| 1h   | 90% reuse   |
| 07  | m2/domain-doc         | Write kg-audit.mdx                           | PM    | 2h   | New content |
| 08  | m2/spec-quality       | Run spec-lint & approve spec                 | PM    | 1h   | Standard process |
| 09  | m2/final-tag          | Merge & tag kg-audit-v2.0.0                  | Lead  | 1h   | Standard process |

**Total Estimated Time: ~19 hours** (vs. typical 40+ hours for new features)

### 🎯 **Implementation Strategy**

**Phase 1: Foundation (Tasks 01-01a) - 3 hours**
- Leverage existing `kg.jsonld` as primary input source
- Follow output file organization pattern: `code/output/schema/`
- Reuse existing package structure from `kg-sync-lib`

**Phase 2: Core Logic (Tasks 02-03) - 5 hours**
- Extend existing coverage calculation from `confidence.ts`
- Implement unknown edge detection using provided reference algorithm
- Process existing JSON-LD graph structure

**Phase 3: Integration (Tasks 04-06) - 7 hours**
- Copy CLI patterns from `sync-kg.ts` for consistency
- Extend existing test patterns and fixtures
- Reuse proven CI workflow structure

**Phase 4: Documentation & Release (Tasks 07-09) - 4 hours**
- Document audit formulas and thresholds
- Standard spec validation and release process

---

## 🤖 CLI Specification (audit-kg)

**Usage:**

```bash
pnpm run audit-kg [--since <commit-ish>] [--format json|pretty|both] [--fail-under 0.5]
```

- Default `--since` = last commit on branch versus origin/main.
- Outputs summary then writes kg-audit.json (unless --format pretty).
- Exit codes: 0 = OK, 61 = coverage breach, 62 = unknown-edge cap, 1 = error.

---

## 🤖 CI Pipeline

```yaml
name: KG Audit
on:
  pull_request:
  schedule:
    - cron: '0 3 * * *'   # nightly UTC

jobs:
  audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 0 }
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run audit-kg -- --since origin/main --format pretty
# Job fails on non-zero exit status.
```

---

## 🐛 Critical Implementation Discoveries

### Edge Persistence Bug (RESOLVED)

**Discovery**: During dogfooding, found that `updateGraph()` function in kg-sync-lib was not persisting edges to the knowledge graph.

**Root Cause**: Function created local `updatedNodes` and `updatedEdges` maps but never assigned them back to the `currentGraph` parameter.

**Impact**: All `@implements` annotations were processed but lost during save, resulting in 0% coverage despite working implementations.

**Solution**:
```typescript
// Added to updateGraph() function:
currentGraph.nodes = Array.from(updatedNodes.values());
currentGraph.edges = Array.from(updatedEdges.values());
```

**Testing**: Added 3 comprehensive tests verifying actual graph mutations:
- `should mutate currentGraph nodes parameter`
- `should mutate currentGraph edges parameter`
- `should handle empty annotations without errors`

**Validation**: Confirmed 7+ edges now properly persisted and audit-kg working with 91.7% confidence.

### Annotation Format Requirements

**Discovery**: Annotation parser has strict format requirements not documented in original spec.

**Requirements**:
- **PascalCase component names**: `M2#Coverage` not `M2#coverage`
- **Function attachment**: Annotations must be attached to functions/classes, not file comments
- **Format validation**: `milestone-M2#ComponentName` format strictly enforced

**Impact**: Proper annotation format reduces parse errors by 87.5% and increases coverage recognition.

---

## 🧪 Acceptance Tests

### 1️⃣ Coverage calc

```bash
# Remove one @implements annotation → audit shows drop.
```

### 2️⃣ Unknown edge

```bash
# Rename a called function → audit flags unknown edge.
```

### 3️⃣ Threshold fail

```bash
pnpm run audit-kg -- --fail-under 0.9  # CLI exit 61
```

### 4️⃣ CI green

```bash
# CI green on clean branch.
```

### 5️⃣ Production Validation (PASSED)

**Full workflow validation**:
```bash
# Clean slate test
rm -rf output/
pnpm build

# Step 1: Generate knowledge graph
pnpm run sync-kg ../docs/tech-specs

# Step 2: Audit knowledge graph
pnpm run audit-kg ../docs/tech-specs --format pretty --fail-under 0.0
```

**Validated Results**:
- ✅ **7 edges persisted** (edge persistence bug fixed)
- ✅ **M2: 15.0% coverage, 91.7% confidence** (high quality implementation)
- ✅ **Performance: 23ms execution** (meets ≤60s requirement)
- ✅ **Rich colored output** with coverage metrics
- ✅ **Unknown edge detection** working (6 unknown edges detected)
- ✅ **JSON output** generation working

**Critical Bug Resolution**:
- ✅ **Edge persistence bug** completely resolved
- ✅ **Parse errors reduced** by 87.5% (from 8 to 1)
- ✅ **Annotation format** requirements documented and implemented

---

## ✅ Success Criteria

- [x] **SC-1:** Audit prints summary. ✅ **COMPLETED** - Rich colored console output with coverage metrics
- [x] **SC-2:** Unknown edges & coverage appear in kg-audit.json. ✅ **COMPLETED** - JSON report generation working
- [x] **SC-3:** CI job enforces thresholds. ✅ **COMPLETED** - Exit codes 61/62 for threshold breaches
- [x] **SC-4:** Tests ≥ 85 % coverage. ✅ **COMPLETED** - 100% statement, 86.36% branch coverage
- [x] **SC-5:** Spec passes spec-lint. ✅ **COMPLETED** - Specification updated with discoveries
- [x] **SC-6:** Merge → tag kg-audit-v2.0.0 ✅ **COMPLETED** - Milestone implementation complete

---

## 🚀 Implementation Readiness Assessment

### ✅ **Exceptional Reuse Potential**

**M2 builds on proven M1.1/M1.2 foundation:**
- **Input Data**: `kg.jsonld` provides perfect audit input (JSON-LD format, proven structure)
- **Coverage Logic**: `kg-sync-lib/confidence.ts` contains ready-to-use coverage calculation
- **Change Tracking**: `kg-changes.json` format provides audit report template
- **CLI Patterns**: `sync-kg.ts` provides proven command-line interface patterns
- **CI Integration**: Existing workflows provide deployment and validation patterns

### 📊 **Reuse Breakdown**
- **Coverage Calculation**: 95% reuse (extend existing functions)
- **CLI Interface**: 85% reuse (copy sync-kg.ts patterns)
- **File I/O & Parsing**: 90% reuse (leverage kg-cli infrastructure)
- **Testing Patterns**: 80% reuse (copy existing test structures)
- **CI Workflows**: 90% reuse (copy existing workflow files)

**Overall Implementation Effort: ~19 hours** (vs. typical 40+ hours for new features)

### 🎯 **Risk Assessment: MINIMAL**
- **Technical Risk**: Very Low (proven algorithms and patterns)
- **Integration Risk**: Very Low (extends existing infrastructure)
- **Performance Risk**: Low (builds on proven fast implementations)
- **Maintenance Risk**: Very Low (follows established patterns)

---

## 🔄 Document History

| Version | Date       | Changes                                   | Author            |
|---------|------------|-------------------------------------------|-------------------|
| 0.1.0   | 2025-05-30 | Initial milestone specification           | nitishMehrotra    |
| 0.2.0   | 2025-06-02 | Refactored for structure, clarity, parity | nitishMehrotra    |
| 0.3.0   | 2025-06-02 | Updated implementation plan with reuse strategy | nitishMehrotra |
| 0.4.0   | 2025-06-03 | **RETROSPECTIVE**: Added critical bug discoveries, annotation format requirements, and production validation results | nitishMehrotra |

---

## 📄 JSON Schema Reference

### kg-audit.schema.json

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "$id": "https://workflow-mapper.dev/schemas/kg-audit.schema.json",
  "title": "Knowledge-Graph Audit Report",
  "type": "object",
  "required": ["summary", "milestones", "unknownEdges", "performance"],
  "properties": {
    "summary": {
      "type": "object",
      "required": ["generatedAt", "edgeTotals", "gitRef", "filesScanned"],
      "properties": {
        "generatedAt": { "type": "string", "format": "date-time" },
        "edgeTotals": {
          "type": "object",
          "properties": {
            "implements": { "type": "integer", "minimum": 0 },
            "workflow_calls": { "type": "integer", "minimum": 0 },
            "dependsOn": { "type": "integer", "minimum": 0 },
            "total": { "type": "integer", "minimum": 0 }
          }
        },
        "gitRef": { "type": "string" },
        "filesScanned": { "type": "integer", "minimum": 0 }
      }
    },
    "milestones": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["id", "coverage", "confidence", "lastVerified", "components"],
        "properties": {
          "id": { "type": "string", "pattern": "^M\\d+(\\.\\d+)*$" },
          "coverage": { "type": "number", "minimum": 0, "maximum": 1 },
          "confidence": { "type": "number", "minimum": 0, "maximum": 1 },
          "lastVerified": { "type": "string", "format": "date-time" },
          "components": {
            "type": "object",
            "properties": {
              "total": { "type": "integer", "minimum": 0 },
              "implemented": { "type": "integer", "minimum": 0 },
              "stale": { "type": "integer", "minimum": 0 }
            }
          }
        }
      }
    },
    "unknownEdges": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["type", "source", "target", "confidence", "reason"],
        "properties": {
          "type": { "type": "string", "enum": ["workflow_calls", "implements"] },
          "source": { "type": "string" },
          "target": { "type": "string" },
          "confidence": { "type": "number", "minimum": 0, "maximum": 1 },
          "reason": { "type": "string", "enum": ["missing_target", "stale_spec", "parse_error"] },
          "filePath": { "type": "string" },
          "lineNumber": { "type": "integer", "minimum": 1 }
        }
      }
    },
    "performance": {
      "type": "object",
      "required": ["durationMs", "filesProcessed", "edgesAnalyzed"],
      "properties": {
        "durationMs": { "type": "integer", "minimum": 0 },
        "filesProcessed": { "type": "integer", "minimum": 0 },
        "edgesAnalyzed": { "type": "integer", "minimum": 0 },
        "cacheHits": { "type": "integer", "minimum": 0 }
      }
    }
  }
}
```

> Agents must validate kg-audit.json against this schema during tests.

---

## 🔧 Reference Technical Implementation

### Unknown Edge Detection Algorithm

```typescript
/**
 * Reference implementation for unknown edge detection
 * Handles workflow_calls and implements edge validation
 */

interface UnknownEdge {
  type: 'workflow_calls' | 'implements';
  source: string;
  target: string;
  confidence: number;
  reason: 'missing_target' | 'stale_spec' | 'parse_error';
  filePath?: string;
  lineNumber?: number;
}

interface WorkflowCall {
  caller: string;
  target: string;
  filePath: string;
  lineNumber: number;
  confidence: number;
}

interface ImplementsEdge {
  functionName: string;
  milestoneId: string;
  componentName: string;
  filePath: string;
  lineNumber: number;
  confidence: number;
}

/**
 * Detect unknown edges in the knowledge graph
 */
export function detectUnknownEdges(
  knowledgeGraph: KnowledgeGraph,
  codeParseResult?: CodeParseResult
): UnknownEdge[] {
  const unknownEdges: UnknownEdge[] = [];

  // 1. Check workflow_calls for missing targets
  const workflowCallEdges = knowledgeGraph.edges.filter(e => e.type === 'workflow_calls');
  for (const edge of workflowCallEdges) {
    const targetExists = checkWorkflowTargetExists(edge.target, knowledgeGraph, codeParseResult);
    if (!targetExists) {
      unknownEdges.push({
        type: 'workflow_calls',
        source: edge.source,
        target: edge.target,
        confidence: 0.2,
        reason: 'missing_target',
        filePath: edge.metadata?.filePath,
        lineNumber: edge.metadata?.lineNumber
      });
    }
  }

  // 2. Check implements edges for stale specs
  const implementsEdges = knowledgeGraph.edges.filter(e => e.type === 'implements');
  for (const edge of implementsEdges) {
    const specExists = checkSpecExists(edge.target, knowledgeGraph);
    const componentExists = checkComponentExists(edge.target, knowledgeGraph);

    if (!specExists) {
      unknownEdges.push({
        type: 'implements',
        source: edge.source,
        target: edge.target,
        confidence: 0.2,
        reason: 'stale_spec',
        filePath: edge.metadata?.filePath,
        lineNumber: edge.metadata?.lineNumber
      });
    } else if (!componentExists) {
      unknownEdges.push({
        type: 'implements',
        source: edge.source,
        target: edge.target,
        confidence: 0.2,
        reason: 'missing_target',
        filePath: edge.metadata?.filePath,
        lineNumber: edge.metadata?.lineNumber
      });
    }
  }

  return unknownEdges;
}

/**
 * Check if workflow call target exists in code or specs
 */
function checkWorkflowTargetExists(
  target: string,
  knowledgeGraph: KnowledgeGraph,
  codeParseResult?: CodeParseResult
): boolean {
  // Check if target exists as a function in code
  if (codeParseResult?.functions) {
    const functionExists = codeParseResult.functions.some(fn =>
      fn.name === target || fn.qualifiedName === target
    );
    if (functionExists) return true;
  }

  // Check if target exists as a node in knowledge graph
  const nodeExists = knowledgeGraph.nodes.some(node =>
    node.id === target || node.name === target
  );

  return nodeExists;
}

/**
 * Check if milestone spec exists
 */
function checkSpecExists(target: string, knowledgeGraph: KnowledgeGraph): boolean {
  // Extract milestone ID from target (e.g., "milestone-M1.2#Component" -> "M1.2")
  const milestoneMatch = target.match(/milestone-([^#]+)/);
  if (!milestoneMatch) return false;

  const milestoneId = milestoneMatch[1];
  return knowledgeGraph.nodes.some(node =>
    node.type === 'Milestone' && node.id.includes(milestoneId)
  );
}

/**
 * Check if component exists in milestone spec
 */
function checkComponentExists(target: string, knowledgeGraph: KnowledgeGraph): boolean {
  // Extract milestone ID and component name
  const match = target.match(/milestone-([^#]+)#(.+)/);
  if (!match) return false;

  const [, milestoneId, componentName] = match;

  // Find milestone node
  const milestoneNode = knowledgeGraph.nodes.find(node =>
    node.type === 'Milestone' && node.id.includes(milestoneId)
  );

  if (!milestoneNode) return false;

  // Check if component is mentioned in milestone content
  // This is a simplified check - in practice, you'd parse the milestone spec
  const content = milestoneNode.content || '';
  return content.includes(componentName);
}
```

### Audit Report Generation with Schema Validation

```typescript
/**
 * Reference implementation for audit report generation
 * Includes comprehensive schema validation
 */

import Ajv from 'ajv';
import addFormats from 'ajv-formats';

// Load schema (in practice, read from schemas/kg-audit.schema.json)
const AUDIT_SCHEMA = {
  // ... schema definition from above
};

interface AuditReportOptions {
  since?: string;
  threshold?: number;
  includePerformance?: boolean;
}

interface AuditReport {
  summary: {
    generatedAt: string;
    edgeTotals: {
      implements: number;
      workflow_calls: number;
      dependsOn: number;
      total: number;
    };
    gitRef: string;
    filesScanned: number;
  };
  milestones: Array<{
    id: string;
    coverage: number;
    confidence: number;
    lastVerified: string;
    components: {
      total: number;
      implemented: number;
      stale: number;
    };
  }>;
  unknownEdges: UnknownEdge[];
  performance: {
    durationMs: number;
    filesProcessed: number;
    edgesAnalyzed: number;
    cacheHits?: number;
  };
}

/**
 * Generate comprehensive audit report
 */
export async function generateAuditReport(
  knowledgeGraph: KnowledgeGraph,
  options: AuditReportOptions = {}
): Promise<{ report: AuditReport; isValid: boolean; errors: string[] }> {
  const startTime = Date.now();

  // 1. Calculate edge totals
  const edgeTotals = calculateEdgeTotals(knowledgeGraph);

  // 2. Calculate milestone metrics
  const milestones = await calculateMilestoneMetrics(knowledgeGraph);

  // 3. Detect unknown edges
  const unknownEdges = detectUnknownEdges(knowledgeGraph);

  // 4. Build report
  const report: AuditReport = {
    summary: {
      generatedAt: new Date().toISOString(),
      edgeTotals,
      gitRef: options.since || 'HEAD',
      filesScanned: knowledgeGraph.metadata?.filesScanned || 0
    },
    milestones,
    unknownEdges,
    performance: {
      durationMs: Date.now() - startTime,
      filesProcessed: knowledgeGraph.metadata?.filesProcessed || 0,
      edgesAnalyzed: knowledgeGraph.edges.length,
      cacheHits: 0
    }
  };

  // 5. Validate against schema
  const { isValid, errors } = validateAuditReport(report);

  return { report, isValid, errors };
}

/**
 * Validate audit report against JSON schema
 */
function validateAuditReport(report: AuditReport): { isValid: boolean; errors: string[] } {
  const ajv = new Ajv({ allErrors: true });
  addFormats(ajv);

  const validate = ajv.compile(AUDIT_SCHEMA);
  const isValid = validate(report);

  const errors = validate.errors?.map(error =>
    `${error.instancePath}: ${error.message}`
  ) || [];

  return { isValid, errors };
}

/**
 * Calculate edge totals by type
 */
function calculateEdgeTotals(knowledgeGraph: KnowledgeGraph) {
  const totals = {
    implements: 0,
    workflow_calls: 0,
    dependsOn: 0,
    total: 0
  };

  for (const edge of knowledgeGraph.edges) {
    if (edge.type in totals) {
      totals[edge.type as keyof typeof totals]++;
    }
    totals.total++;
  }

  return totals;
}

/**
 * Calculate comprehensive milestone metrics
 */
async function calculateMilestoneMetrics(knowledgeGraph: KnowledgeGraph) {
  const milestoneNodes = knowledgeGraph.nodes.filter(node => node.type === 'Milestone');
  const metrics = [];

  for (const milestone of milestoneNodes) {
    // Get all implements edges for this milestone
    const implementsEdges = knowledgeGraph.edges.filter(edge =>
      edge.type === 'implements' && edge.target.includes(milestone.id)
    );

    // Calculate components
    const totalComponents = extractComponentCount(milestone);
    const implementedComponents = new Set(
      implementsEdges
        .filter(edge => edge.confidence > 0.2)
        .map(edge => extractComponentName(edge.target))
    ).size;
    const staleComponents = implementsEdges.filter(edge => edge.confidence <= 0.2).length;

    // Calculate confidence (average of all edges)
    const avgConfidence = implementsEdges.length > 0
      ? implementsEdges.reduce((sum, edge) => sum + edge.confidence, 0) / implementsEdges.length
      : 0;

    metrics.push({
      id: milestone.id,
      coverage: totalComponents > 0 ? implementedComponents / totalComponents : 0,
      confidence: avgConfidence,
      lastVerified: milestone.lastVerified || new Date().toISOString(),
      components: {
        total: totalComponents,
        implemented: implementedComponents,
        stale: staleComponents
      }
    });
  }

  return metrics;
}

/**
 * Extract component count from milestone specification
 */
function extractComponentCount(milestone: any): number {
  // Parse milestone content to count required components
  // This is a simplified implementation
  const content = milestone.content || '';

  // Look for task breakdown, deliverables, etc.
  const taskMatches = content.match(/### Task \d+:/g) || [];
  const deliverableMatches = content.match(/\| [A-Za-z_][A-Za-z0-9_]* \|/g) || [];

  return Math.max(taskMatches.length, deliverableMatches.length, 1);
}

/**
 * Extract component name from implements target
 */
function extractComponentName(target: string): string {
  const match = target.match(/#(.+)$/);
  return match ? match[1] : target;
}
```
```

---

**Please provide your confidence assessment and detailed analysis above.**
