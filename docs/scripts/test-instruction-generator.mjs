#!/usr/bin/env node

/**
 * Test script for the enhanced instruction generator
 * Tests various scenarios and validates output quality
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class InstructionGeneratorTester {
  constructor() {
    this.testResults = [];
    this.generatorPath = path.join(__dirname, "instruction-generator.mjs");
  }

  async runAllTests() {
    console.log("🧪 Testing Enhanced Instruction Generator");
    console.log("=".repeat(50));

    await this.testMilestoneValidation();
    await this.testInstructionGeneration();
    await this.testValidationWarnings();
    await this.testStructuredDataExtraction();

    this.printSummary();
  }

  async testMilestoneValidation() {
    console.log("\n📋 Testing Milestone Validation...");

    // Test with existing milestone
    const existingMilestone = "docs/tech-specs/milestones/milestone-M1.2.mdx";
    if (fs.existsSync(existingMilestone)) {
      const result = await this.generateInstructions(
        existingMilestone,
        "augment"
      );
      this.recordTest(
        "Existing milestone parsing",
        result.success,
        result.message
      );
    } else {
      this.recordTest(
        "Existing milestone parsing",
        false,
        "M1.2 milestone not found"
      );
    }

    // Test with non-existent milestone
    const nonExistentMilestone =
      "docs/tech-specs/milestones/milestone-NONEXISTENT.mdx";
    const result2 = await this.generateInstructions(
      nonExistentMilestone,
      "augment"
    );
    this.recordTest(
      "Non-existent milestone handling",
      !result2.success,
      "Should fail gracefully"
    );
  }

  async testInstructionGeneration() {
    console.log("\n📝 Testing Instruction Generation...");

    const milestones = [
      "docs/tech-specs/milestones/milestone-M1.2.mdx",
      "docs/tech-specs/milestones/milestone-M2.mdx"
    ];

    const agents = ["augment", "cursor", "copilot"];

    for (const milestone of milestones) {
      if (fs.existsSync(milestone)) {
        for (const agent of agents) {
          const result = await this.generateInstructions(milestone, agent);
          this.recordTest(
            `${path.basename(milestone)} + ${agent}`,
            result.success,
            result.message
          );
        }
      }
    }
  }

  async testValidationWarnings() {
    console.log("\n⚠️ Testing Validation Warnings...");

    // Create a test milestone with missing fields
    const testMilestone = this.createTestMilestone();
    const result = await this.generateInstructions(testMilestone, "augment");

    this.recordTest(
      "Validation warnings detection",
      result.success,
      result.message
    );

    // Clean up test file
    if (fs.existsSync(testMilestone)) {
      fs.unlinkSync(testMilestone);
    }
  }

  async testStructuredDataExtraction() {
    console.log("\n🔍 Testing Structured Data Extraction...");

    // Test with M1.2 which has good structure
    const milestone = "docs/tech-specs/milestones/milestone-M1.2.mdx";
    if (fs.existsSync(milestone)) {
      const result = await this.generateInstructions(milestone, "augment");

      if (result.success && result.outputFile) {
        const content = fs.readFileSync(result.outputFile, "utf8");

        // Check for structured data inclusion
        const hasSuccessCriteria = content.includes(
          "### 🎯 Success Criteria Overview"
        );
        const hasDeliverables = content.includes("### 📦 Key Deliverables");
        const hasTaskBreakdown = content.includes(
          "**Suggested Task Breakdown**"
        );

        this.recordTest(
          "Success criteria extraction",
          hasSuccessCriteria,
          "Should include success criteria"
        );
        this.recordTest(
          "Deliverables extraction",
          hasDeliverables,
          "Should include deliverables"
        );
        this.recordTest(
          "Task breakdown extraction",
          hasTaskBreakdown,
          "Should include task breakdown"
        );
      }
    }
  }

  async generateInstructions(milestoneFile, agentType) {
    try {
      const outputFile = `work-log/test-instructions-${agentType}.md`;

      // Import the generator module
      const generatorModule = await import(this.generatorPath);

      // The class is the default export, not a named export
      const InstructionGenerator =
        generatorModule.default || generatorModule.InstructionGenerator;

      if (!InstructionGenerator) {
        throw new Error("Could not find InstructionGenerator class in module");
      }

      const generator = new InstructionGenerator();
      const success = await generator.generateInstructions(
        milestoneFile,
        agentType,
        outputFile
      );

      return {
        success,
        outputFile: success ? outputFile : null,
        message: success ? "Generated successfully" : "Generation failed"
      };
    } catch (error) {
      return {
        success: false,
        outputFile: null,
        message: error.message
      };
    }
  }

  createTestMilestone() {
    const testContent = `---
title: Test Milestone
status: Draft
---

# Test Milestone

## 🎯 Definition of Done

1. **Test Criteria**: This is a test criterion

## 📦 Deliverables

| Path | Description |
|------|-------------|
| test/file.js | Test file |

## 🔨 Task Breakdown

| # | Branch | Task | Owner | Est |
|---|--------|------|-------|-----|
| 01 | test/task-01 | Test task | Test | 1d |
`;

    const testFile = "docs/tech-specs/milestones/milestone-TEST-TEMP.mdx";
    fs.writeFileSync(testFile, testContent);
    return testFile;
  }

  recordTest(testName, passed, message) {
    this.testResults.push({ testName, passed, message });
    const status = passed ? "✅ PASS" : "❌ FAIL";
    console.log(`  ${status}: ${testName} - ${message}`);
  }

  printSummary() {
    console.log("\n" + "=".repeat(50));
    console.log("📊 Test Summary");
    console.log("=".repeat(50));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter((r) => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(
      `Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`
    );

    if (failedTests > 0) {
      console.log("\n❌ Failed Tests:");
      this.testResults
        .filter((r) => !r.passed)
        .forEach((test) => {
          console.log(`  - ${test.testName}: ${test.message}`);
        });
    }

    console.log("\n🎉 Testing Complete!");
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new InstructionGeneratorTester();
  tester.runAllTests().catch((error) => {
    console.error("Test execution failed:", error);
    process.exit(1);
  });
}
