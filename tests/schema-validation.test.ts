/**
 * @fileoverview Basic schema validation tests for Task 01a
 * @implements milestone-M2#SchemaValidation
 */

import { validateAuditReport } from '../src/auditReport.js';
import type { AuditReport } from '../src/types.js';

describe('Schema Validation', () => {
  it('should validate a valid audit report', async () => {
    const validReport: AuditReport = {
      summary: {
        generatedAt: new Date().toISOString(),
        edgeTotals: {
          implements: 5,
          workflow_calls: 3,
          dependsOn: 2,
          total: 10
        },
        gitRef: 'HEAD',
        filesScanned: 100
      },
      milestones: [
        {
          id: 'M1.1',
          coverage: 0.85,
          confidence: 0.92,
          lastVerified: new Date().toISOString(),
          unknownEdgeCount: 1,
          auditTimestamp: new Date().toISOString(),
          components: {
            total: 10,
            implemented: 8,
            stale: 1
          },
          implementedComponents: 8
        }
      ],
      unknownEdges: [
        {
          type: 'workflow_calls',
          source: 'function1',
          target: 'missing_function',
          confidence: 0.2,
          reason: 'missing_target',
          filePath: 'src/test.ts',
          lineNumber: 42
        }
      ],
      performance: {
        durationMs: 1500,
        filesProcessed: 100,
        edgesAnalyzed: 250,
        cacheHits: 50
      }
    };

    const result = await validateAuditReport(validReport);
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it('should reject an invalid audit report', async () => {
    const invalidReport = {
      summary: {
        // Missing required fields
        generatedAt: 'invalid-date'
      },
      // Missing required fields
    } as any;

    const result = await validateAuditReport(invalidReport);
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });

  it('should handle schema loading errors gracefully', async () => {
    // Test with process.cwd() pointing to wrong location
    const originalCwd = process.cwd();
    
    try {
      // Mock process.cwd to return invalid path
      jest.spyOn(process, 'cwd').mockReturnValue('/invalid/path');
      
      const validReport: AuditReport = {
        summary: {
          generatedAt: new Date().toISOString(),
          edgeTotals: { implements: 0, workflow_calls: 0, dependsOn: 0, total: 0 },
          gitRef: 'HEAD',
          filesScanned: 0
        },
        milestones: [],
        unknownEdges: [],
        performance: {
          durationMs: 100,
          filesProcessed: 0,
          edgesAnalyzed: 0
        }
      };

      const result = await validateAuditReport(validReport);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Schema validation failed');
    } finally {
      // Restore original cwd
      jest.restoreAllMocks();
    }
  });
});
