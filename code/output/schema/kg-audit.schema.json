{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://workflow-mapper.dev/schemas/kg-audit.schema.json", "title": "Knowledge-Graph Audit Report", "type": "object", "required": ["summary", "milestones", "unknown<PERSON><PERSON>", "performance"], "properties": {"summary": {"type": "object", "required": ["generatedAt", "edgeTotals", "gitRef", "filesScanned"], "properties": {"generatedAt": {"type": "string", "format": "date-time"}, "edgeTotals": {"type": "object", "properties": {"implements": {"type": "integer", "minimum": 0}, "workflow_calls": {"type": "integer", "minimum": 0}, "dependsOn": {"type": "integer", "minimum": 0}, "total": {"type": "integer", "minimum": 0}}}, "gitRef": {"type": "string"}, "filesScanned": {"type": "integer", "minimum": 0}}}, "milestones": {"type": "array", "items": {"type": "object", "required": ["id", "coverage", "confidence", "lastVerified", "components"], "properties": {"id": {"type": "string", "pattern": "^M\\d+(\\.\\d+)*$"}, "coverage": {"type": "number", "minimum": 0, "maximum": 1}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "lastVerified": {"type": "string", "format": "date-time"}, "components": {"type": "object", "properties": {"total": {"type": "integer", "minimum": 0}, "implemented": {"type": "integer", "minimum": 0}, "stale": {"type": "integer", "minimum": 0}}}}}}, "unknownEdges": {"type": "array", "items": {"type": "object", "required": ["type", "source", "target", "confidence", "reason"], "properties": {"type": {"type": "string", "enum": ["workflow_calls", "implements"]}, "source": {"type": "string"}, "target": {"type": "string"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "reason": {"type": "string", "enum": ["missing_target", "stale_spec", "parse_error"]}, "filePath": {"type": "string"}, "lineNumber": {"type": "integer", "minimum": 1}}}}, "performance": {"type": "object", "required": ["durationMs", "filesProcessed", "edgesAnalyzed"], "properties": {"durationMs": {"type": "integer", "minimum": 0}, "filesProcessed": {"type": "integer", "minimum": 0}, "edgesAnalyzed": {"type": "integer", "minimum": 0}, "cacheHits": {"type": "integer", "minimum": 0}}}}}