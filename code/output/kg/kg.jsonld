{"@context": {"@vocab": "https://workflow-mapper.dev/vocab#", "title": "https://schema.org/name", "description": "https://schema.org/description", "implements": "https://workflow-mapper.dev/vocab#implements", "dependsOn": "https://workflow-mapper.dev/vocab#dependsOn"}, "@graph": [{"@id": "function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph", "@type": "function", "filePath": "code/packages/kg-sync-lib/src/updateGraph.ts", "functionName": "updateGraph", "lastVerified": "2025-06-04T04:30:19.911Z"}, {"@id": "component:M1.2#GraphUpdateCore", "@type": "component", "milestoneId": "M1.2", "componentName": "GraphUpdateCore", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/auditReport.ts", "functionName": "generateAuditReport", "lastVerified": "2025-06-04T06:18:39.721Z"}, {"@id": "component:M2#AuditSchema", "@type": "component", "milestoneId": "M2", "componentName": "AuditSchema", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/coverage.ts", "functionName": "calculateAuditCoverage", "lastVerified": "2025-06-04T04:30:19.910Z"}, {"@id": "component:M2#Coverage", "@type": "component", "milestoneId": "M2", "componentName": "Coverage", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/index.ts", "functionName": "initializeAuditLibrary", "lastVerified": "2025-06-04T04:30:19.910Z"}, {"@id": "component:M2#AuditLib", "@type": "component", "milestoneId": "M2", "componentName": "AuditLib", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/types.ts", "functionName": "validateAuditTypes", "lastVerified": "2025-06-04T04:30:19.910Z"}, {"@id": "function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/unknownEdges.ts", "functionName": "detectUnknownEdges", "lastVerified": "2025-06-04T04:30:19.910Z"}, {"@id": "component:M2#UnknownEdges", "@type": "component", "milestoneId": "M2", "componentName": "<PERSON><PERSON><PERSON>", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-cli/src/audit-kg.ts#", "@type": "function", "filePath": "code/packages/kg-cli/src/audit-kg.ts", "functionName": "", "lastVerified": "2025-06-04T04:30:19.911Z"}, {"@id": "component:M2#Cli", "@type": "component", "milestoneId": "M2", "componentName": "<PERSON><PERSON>", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "component:M2#AuditCli", "@type": "component", "milestoneId": "M2", "componentName": "AuditCli", "lastVerified": "2025-06-03T18:46:45.696Z"}, {"@id": "implements:function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph->component:M1.2#GraphUpdateCore", "@type": "implements", "source": "function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph", "target": "component:M1.2#GraphUpdateCore", "confidence": 0.2, "lastVerified": "2025-06-04T06:18:16.997Z", "stale": true, "staleReason": "annotation_removed"}, {"@id": "implements:function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport->component:M2#AuditSchema", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport", "target": "component:M2#AuditSchema", "confidence": 1, "lastVerified": "2025-06-04T06:18:39.724Z", "stale": false}, {"@id": "implements:function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage->component:M2#Coverage", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage", "target": "component:M2#Coverage", "confidence": 0.2, "lastVerified": "2025-06-04T06:18:16.997Z", "stale": true, "staleReason": "annotation_removed"}, {"@id": "implements:function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary->component:M2#AuditLib", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary", "target": "component:M2#AuditLib", "confidence": 0.2, "lastVerified": "2025-06-04T06:18:16.997Z", "stale": true, "staleReason": "annotation_removed"}, {"@id": "implements:function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes->component:M2#AuditLib", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes", "target": "component:M2#AuditLib", "confidence": 0.2, "lastVerified": "2025-06-04T06:18:16.997Z", "stale": true, "staleReason": "annotation_removed"}, {"@id": "implements:function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges->component:M2#UnknownEdges", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges", "target": "component:M2#UnknownEdges", "confidence": 0.2, "lastVerified": "2025-06-04T06:18:16.997Z", "stale": true, "staleReason": "annotation_removed"}, {"@id": "implements:function:code/packages/kg-cli/src/audit-kg.ts#->component:M2#Cli", "@type": "implements", "source": "function:code/packages/kg-cli/src/audit-kg.ts#", "target": "component:M2#Cli", "confidence": 0.2, "lastVerified": "2025-06-03T14:24:14.069Z", "stale": true, "staleReason": "annotation_removed"}, {"@id": "implements:function:code/packages/kg-cli/src/audit-kg.ts#->component:M2#AuditCli", "@type": "implements", "source": "function:code/packages/kg-cli/src/audit-kg.ts#", "target": "component:M2#AuditCli", "confidence": 0.2, "lastVerified": "2025-06-04T06:18:16.997Z", "stale": true, "staleReason": "annotation_removed"}]}