{"@context": {"@vocab": "https://workflow-mapper.dev/vocab#", "title": "https://schema.org/name", "description": "https://schema.org/description", "implements": "https://workflow-mapper.dev/vocab#implements", "dependsOn": "https://workflow-mapper.dev/vocab#dependsOn"}, "@graph": [{"@id": "function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph", "@type": "function", "filePath": "code/packages/kg-sync-lib/src/updateGraph.ts", "functionName": "updateGraph", "lastVerified": "2025-06-03T06:55:38.640Z"}, {"@id": "component:M1.2#GraphUpdateCore", "@type": "component", "milestoneId": "M1.2", "componentName": "GraphUpdateCore", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/auditReport.ts", "functionName": "generateAuditReport", "lastVerified": "2025-06-03T06:55:38.637Z"}, {"@id": "component:M2#AuditSchema", "@type": "component", "milestoneId": "M2", "componentName": "AuditSchema", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/coverage.ts", "functionName": "calculateAuditCoverage", "lastVerified": "2025-06-03T06:55:38.638Z"}, {"@id": "component:M2#Coverage", "@type": "component", "milestoneId": "M2", "componentName": "Coverage", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/index.ts", "functionName": "initializeAuditLibrary", "lastVerified": "2025-06-03T06:55:38.638Z"}, {"@id": "component:M2#AuditLib", "@type": "component", "milestoneId": "M2", "componentName": "AuditLib", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/types.ts", "functionName": "validateAuditTypes", "lastVerified": "2025-06-03T06:55:38.638Z"}, {"@id": "function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges", "@type": "function", "filePath": "code/packages/kg-audit-lib/src/unknownEdges.ts", "functionName": "detectUnknownEdges", "lastVerified": "2025-06-03T06:55:38.638Z"}, {"@id": "component:M2#UnknownEdges", "@type": "component", "milestoneId": "M2", "componentName": "<PERSON><PERSON><PERSON>", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "function:code/packages/kg-cli/src/audit-kg.ts#", "@type": "function", "filePath": "code/packages/kg-cli/src/audit-kg.ts", "functionName": "", "lastVerified": "2025-06-03T06:55:38.639Z"}, {"@id": "component:M2#Cli", "@type": "component", "milestoneId": "M2", "componentName": "<PERSON><PERSON>", "lastVerified": "2025-06-03T05:45:09.962Z"}, {"@id": "implements:function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph->component:M1.2#GraphUpdateCore", "@type": "implements", "source": "function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph", "target": "component:M1.2#GraphUpdateCore", "confidence": 1, "lastVerified": "2025-06-03T06:55:38.641Z", "stale": false}, {"@id": "implements:function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport->component:M2#AuditSchema", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport", "target": "component:M2#AuditSchema", "confidence": 1, "lastVerified": "2025-06-03T06:55:38.641Z", "stale": false}, {"@id": "implements:function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage->component:M2#Coverage", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage", "target": "component:M2#Coverage", "confidence": 1, "lastVerified": "2025-06-03T06:55:38.641Z", "stale": false}, {"@id": "implements:function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary->component:M2#AuditLib", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary", "target": "component:M2#AuditLib", "confidence": 1, "lastVerified": "2025-06-03T06:55:38.641Z", "stale": false}, {"@id": "implements:function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes->component:M2#AuditLib", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes", "target": "component:M2#AuditLib", "confidence": 1, "lastVerified": "2025-06-03T06:55:38.641Z", "stale": false}, {"@id": "implements:function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges->component:M2#UnknownEdges", "@type": "implements", "source": "function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges", "target": "component:M2#UnknownEdges", "confidence": 1, "lastVerified": "2025-06-03T06:55:38.641Z", "stale": false}, {"@id": "implements:function:code/packages/kg-cli/src/audit-kg.ts#->component:M2#Cli", "@type": "implements", "source": "function:code/packages/kg-cli/src/audit-kg.ts#", "target": "component:M2#Cli", "confidence": 0.5, "lastVerified": "2025-06-03T06:55:38.641Z", "stale": false}]}