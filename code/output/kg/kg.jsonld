{"@context": {"@vocab": "https://workflow-mapper.dev/vocab#", "title": "https://schema.org/name", "description": "https://schema.org/description", "implements": "https://workflow-mapper.dev/vocab#implements", "dependsOn": "https://workflow-mapper.dev/vocab#dependsOn"}, "@graph": [{"@id": "spec----docs-tech-specs-adrs-adr-001-monorepo-mdx-adr-001---monorepo-structure-with-pnpm-workspaces", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-001-monorepo.mdx", "title": "ADR-001 — Monorepo Structure with pnpm Workspaces", "description": "Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "monorepo"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-002-typescript-mdx-adr-002---typescript-first-development", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-002-typescript.mdx", "title": "ADR-002 — TypeScript-First Development", "description": "Decision to use strict TypeScript across frontend, backend, and shared code.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "typescript"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-003-jsonld-mdx-adr-003---json-ld-for-graph-representation", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-003-jsonld.mdx", "title": "ADR-003 — JSON-LD for Graph Representation", "description": "Decision to use JSON-LD as the canonical format for representing workflow graphs.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "data-format"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-004-consolidated-gitignore-strateg-mdx-adr-004---consolidated--gitignore-strategy", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-004-consolidated-gitignore-strateg.mdx", "title": "ADR-004 — Consolidated .gitignore Strategy", "description": "Decision to consolidate multiple .gitignore files into a single root-level file with comprehensive coverage for all project tools and technologies.", "created": "2025-05-29T00:00:00.000Z", "updated": "2025-05-29T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "repository-management", "tooling"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-005-knowledge-graph-system-archite-mdx-adr-005---knowledge-graph-system-architecture", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-005-knowledge-graph-system-archite.mdx", "title": "ADR-005 — Knowledge Graph System Architecture", "description": "Unified architecture combining specification parsing (M0.1) and static code analysis (M1.1) for comprehensive knowledge graph generation.", "created": "2025-01-27T00:00:00.000Z", "updated": "2025-01-27T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "knowledge-graph", "static-analysis", "specifications"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-adrs-adr-006-bidirectional-sync-architectur-mdx-adr-006---bidirectional-sync-architecture-for-knowledge-graph", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-006-bidirectional-sync-architectur.mdx", "title": "ADR-006 — Bidirectional Sync Architecture for Knowledge Graph", "description": "Architecture for bidirectional synchronization between specifications and code using incremental git diff and annotation parsing.", "created": "2025-06-01T00:00:00.000Z", "updated": "2025-06-01T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "bidirectional-sync", "knowledge-graph", "git-diff"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-007-jsdoc-annotation-parsing-strat-mdx-adr-007---jsdoc-annotation-parsing-strategy", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-007-jsdoc-annotation-parsing-strat.mdx", "title": "ADR-007 — JSDoc Annotation Parsing Strategy", "description": "Strategy for parsing @implements annotations from JSDoc/TSDoc comments to establish spec-to-code relationships.", "created": "2025-06-01T00:00:00.000Z", "updated": "2025-06-01T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "jsdoc", "annotation-parsing", "comment-parser"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-008-git-diff-integration-for-incre-mdx-adr-008---git-diff-integration-for-incremental-updates", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-008-git-diff-integration-for-incre.mdx", "title": "ADR-008 — Git Diff Integration for Incremental Updates", "description": "Strategy for using git diff to detect changed files and enable incremental knowledge graph updates with optimal performance.", "created": "2025-06-01T00:00:00.000Z", "updated": "2025-06-01T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "git-diff", "incremental-updates", "simple-git"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-log-mdx-architectural-decision-records--adrs-", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/log.mdx", "title": "Architectural Decision Records (ADRs)", "description": "Log of all significant architectural decisions made during development.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.1.0", "status": "Living", "tags": ["architecture", "decisions"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-archived-adrs-adr-007-docusaurus-mdx-adr-007---docusaurus-for-documentation-site", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/archived/adrs/adr-007-docusaurus.mdx", "title": "ADR-007 — Docusaurus for Documentation Site", "description": "Decision to use Docusaurus 2 as the static site generator for rendering technical specifications and project documentation.", "created": "2025-01-25T00:00:00.000Z", "updated": "2025-01-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "documentation", "<PERSON>cusaurus"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-archived-milestones-milestone-m0-1-content-processing-validation-mdx-content-processing-solution---validation-experiment", "@type": "Milestone", "filePath": "../docs/tech-specs/archived/milestones/milestone-m0.1/content-processing-validation.mdx", "title": "Content Processing Solution - Validation Experiment", "description": "Testing and validating the content processing approach before integration", "created": "2025-01-26T00:00:00.000Z", "version": "0.1.0", "status": "Experimental", "tags": ["experiment", "validation", "content-processing"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-archived-milestones-milestone-m0-1-milestone-m0-1-mdx--archived--milestone-m0-1---docusaurus-documentation-site", "@type": "Milestone", "filePath": "../docs/tech-specs/archived/milestones/milestone-m0.1/milestone-M0.1.mdx", "title": "[ARCHIVED] Milestone M0.1 — Docusaurus Documentation Site", "description": "[ARCHIVED] Original attempt at documentation site - superseded by WorkflowMapperAgent approach", "created": "2025-01-25T00:00:00.000Z", "archived": "2025-01-26T00:00:00.000Z", "version": "0.1.0", "status": "Archived", "tags": ["milestone", "documentation", "<PERSON>cusaurus", "archived"], "authors": ["nitishMeh<PERSON><PERSON>"], "archive_reason": "Pivoted to WorkflowMapperAgent foundation after discovering alignment with broader vision", "superseded_by": "milestone-experiment-1.md and upcoming ChatGPT milestone revision"}, {"@id": "spec----docs-tech-specs-archived-milestones-milestone-m0-1-milestone-pivot-decision-mdx-milestone-pivot-decision---from-documentation-to-workflowmapperagent", "@type": "Milestone", "filePath": "../docs/tech-specs/archived/milestones/milestone-m0.1/milestone-pivot-decision.mdx", "title": "Milestone Pivot Decision - From Documentation to WorkflowMapperAgent", "description": "Decision record for pivoting from M0.1 Docusaurus approach to WorkflowMapperAgent foundation", "created": "2025-01-26T00:00:00.000Z", "version": "1.0.0", "status": "Approved", "tags": ["decision", "pivot", "architecture", "workflow-mapper"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-dependencies-mdx-dependencies", "@type": "Specification", "filePath": "../docs/tech-specs/dependencies.mdx"}, {"@id": "spec----docs-tech-specs-domains-code-parser-mdx-code-parser-domain", "@type": "Domain", "filePath": "../docs/tech-specs/domains/code-parser.mdx", "title": "Code Parser Domain", "description": "Static code analysis and knowledge graph generation from source files", "version": "1.0.0", "status": "Active", "created": "2025-01-27", "updated": "2025-01-27", "authors": ["WorkflowMapper Team"], "tags": ["code-analysis", "tree-sitter", "knowledge-graph", "static-analysis"]}, {"@id": "spec----docs-tech-specs-domains-kg-sync-mdx-knowledge-graph-sync-domain", "@type": "Domain", "filePath": "../docs/tech-specs/domains/kg-sync.mdx", "title": "Knowledge Graph Sync Domain", "description": "Bidirectional synchronization between code annotations and milestone specifications", "version": "1.2.0", "status": "Active", "created": "2025-06-01", "updated": "2025-06-01", "authors": ["WorkflowMapper Team"], "tags": ["bidirectional-sync", "annotations", "git-diff", "knowledge-graph", "incremental-updates"]}, {"@id": "spec----docs-tech-specs-guides-agent-configuration-guide-mdx-agent-configuration-guide", "@type": "Specification", "filePath": "../docs/tech-specs/guides/agent-configuration-guide.mdx", "title": "Agent Configuration Guide", "description": "How to configure AI agents with milestone process requirements", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "agents", "configuration", "quality"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-log-mdx-milestone-progress-log", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/log.mdx", "title": "Milestone Progress Log", "description": "Index and progress tracking for all project milestones.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.1.0", "status": "Living", "tags": ["milestones", "progress"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m0-1-mdx-milestone-m0-1---knowledge-graph-bootstrap", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M0.1.mdx", "title": "Milestone M0.1 — Knowledge-<PERSON>rap<PERSON> Bootstrap", "description": "Parse existing MDX specs into a JSON-LD + YAML graph; emit CLI tools every agent can run.", "created": "2025-01-25T00:00:00.000Z", "version": "0.2.0", "status": "Draft", "tags": ["milestone"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m0-mdx-milestone-m0---repository-skeleton---ci", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M0.mdx", "title": "Milestone M0 — Repository Skeleton & CI", "description": "The contractual scope, decisions, and acceptance tests for the very first deliverable.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.5.0", "status": "Completed", "tags": ["milestone"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m1-1-mdx-milestone-m1---static-code-parser---graph-augmenter", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M1.1.mdx", "title": "Milestone M1 — Static Code Parser & Graph Augmenter", "description": "Parse Python, JavaScript, and TypeScript source files with Tree-sitter, extract functions & call-graph, and merge results into the existing KG (kg.jsonld / kg.yaml).", "created": "2025-05-29T00:00:00.000Z", "version": "0.2.0", "status": "Complete", "tags": ["milestone"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m1-2-mdx-milestone-m1-2---bidirectional-sync---incremental-diff", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M1.2.mdx", "title": "Milestone M1.2 — Bidirectional Sync & Incremental Diff", "description": "Link code ↔ specs via annotations, update the KG on every git diff, and emit confidence / coverage metrics.", "created": "2025-05-29T00:00:00.000Z", "updated": "2025-06-01T00:00:00.000Z", "version": "0.3.3", "status": "Draft", "tags": ["milestone"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m2-mdx-milestone-m2---confidence---audit-enhancements", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M2.mdx", "title": "Milestone M2 — Confidence & Audit Enhancements", "description": "Add full coverage / confidence metrics, unknown-edge detection, and automated audit reports for every KG update.", "created": "2025-05-30T00:00:00.000Z", "version": "0.1.0", "status": "Draft", "tags": ["milestone"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m3-mdx-milestone-m3---specification---documentation-generators", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M3.mdx", "title": "Milestone M3 — Specification & Documentation Generators", "description": "Generate OpenAPI/AsyncAPI specs, integration docs, and user manuals directly from the KG.", "created": "2025-05-30T00:00:00.000Z", "version": "0.1.0", "status": "Draft", "tags": ["milestone"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-test-mdx-milestone-test---agent-configuration-validation", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-TEST.mdx", "title": "Milestone TEST - Agent Configuration Validation", "description": "Test milestone to validate streamlined agent configuration system", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Test", "tags": ["test", "validation", "agent-configuration"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-augment-mdx-augment-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/augment.mdx", "title": "Augment Agent Configuration", "description": "Augment Agent specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "augment", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-claude-mdx-claude-anthropic-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/claude.mdx", "title": "Claude/Anthropic Agent Configuration", "description": "Claude/Anthropic specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "claude", "anthropic", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-copilot-mdx-github-copilot-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/copilot.mdx", "title": "GitHub Copilot Agent Configuration", "description": "GitHub Copilot specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "copilot", "github", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-core-mdx-core-agent-rules", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/core.mdx", "title": "Core Agent Rules", "description": "Universal executable rules for all AI software engineering agents", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "core", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-cursor-mdx-cursor-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/cursor.mdx", "title": "Cursor Agent Configuration", "description": "Cursor AI specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "cursor", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-custom-mdx-custom-agent-configuration-template", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/custom.mdx", "title": "Custom Agent Configuration Template", "description": "Template for configuring custom AI agents for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "custom", "template", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-validation-mdx-agent-configuration-validation", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/validation.mdx", "title": "Agent Configuration Validation", "description": "Tools and procedures for validating AI agent configurations", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "validation", "testing"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-core-architectural-decisions-mdx-architectural-decision-process", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/process/core/architectural-decisions.mdx", "title": "Architectural Decision Process", "description": "Process for creating, reviewing, and managing Architectural Decision Records (ADRs)", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "architecture", "decisions", "adr"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["architecture-team"]}, {"@id": "spec----docs-tech-specs-process-core-documentation-mdx-documentation-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/documentation.mdx", "title": "Documentation Process", "description": "Documentation standards, validation requirements, and maintenance procedures", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "documentation", "validation", "standards"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["documentation-team"]}, {"@id": "spec----docs-tech-specs-process-core-error-recovery-mdx-error-recovery---rollback-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/error-recovery.mdx", "title": "Error Recovery & Rollback Process", "description": "Procedures for handling implementation failures, errors, and rollback scenarios", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "error-recovery", "rollback", "incident-response"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["devops-team", "sre-team"]}, {"@id": "spec----docs-tech-specs-process-core-git-workflow-mdx-git-workflow-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/git-workflow.mdx", "title": "Git Workflow Process", "description": "Branching strategies, commit standards, and release processes", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "git", "workflow", "branching"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["development-team"]}, {"@id": "spec----docs-tech-specs-process-core-milestone-implementation-mdx-milestone-implementation-process", "@type": "Milestone", "filePath": "../docs/tech-specs/process/core/milestone-implementation.mdx", "title": "Milestone Implementation Process", "description": "Comprehensive process for executing milestone specifications", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "milestone", "implementation"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["development-team"]}, {"@id": "spec----docs-tech-specs-process-core-quality-assurance-mdx-quality-assurance-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/quality-assurance.mdx", "title": "Quality Assurance Process", "description": "Validation, testing, and review processes for ensuring high-quality deliverables", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "quality", "testing", "validation"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["quality-assurance-team"]}, {"@id": "spec----docs-tech-specs-process-current-status-mdx-current-project-status", "@type": "Specification", "filePath": "../docs/tech-specs/process/current-status.mdx", "title": "Current Project Status", "description": "Current state of the WorkflowMapperAgent development and next steps", "created": "2025-01-26T00:00:00.000Z", "version": "1.0.0", "status": "Active", "tags": ["status", "roadmap", "current-state"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-migration-guide-mdx-agent-configuration-migration-guide", "@type": "Specification", "filePath": "../docs/tech-specs/process/migration-guide.mdx", "title": "Agent Configuration Migration Guide", "description": "Guide for migrating to the streamlined agent configuration system", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["migration", "agent-configuration", "guide"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-optimisation-results-optimisation-result-001-results-mdx-agent-configuration-validation-results", "@type": "Specification", "filePath": "../docs/tech-specs/process/optimisation-results/optimisation-result-001-results.mdx", "title": "Agent Configuration Validation Results", "description": "Comprehensive validation results of streamlined agent configuration system", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Completed", "tags": ["validation", "agent-configuration", "optimization", "results"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-templates-adr-template-mdx-adr-xxx----decision-title-", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/process/templates/adr-template.mdx", "title": "ADR-XXX — <Decision Title>", "description": "<Brief description of the architectural decision>", "created": "<YYYY-MM-DD>", "updated": "<YYYY-MM-DD>", "version": "0.1.0", "status": "Proposed", "tags": ["adr", "architecture"], "authors": ["<author>"]}, {"@id": "spec----docs-tech-specs-process-templates-domain-template-mdx-domain-spec----domain-name-", "@type": "Domain", "filePath": "../docs/tech-specs/process/templates/domain-template.mdx", "title": "Domain Spec — <Domain Name>", "description": "<Brief description of the domain and its scope>", "created": "<YYYY-MM-DD>", "updated": "<YYYY-MM-DD>", "version": "0.0.0", "status": "Draft", "tags": ["domain", "<domain-tag>"], "authors": []}, {"@id": "spec----docs-tech-specs-process-templates-milestone-template-mdx-milestone--id-----one-line-scope-", "@type": "Milestone", "filePath": "../docs/tech-specs/process/templates/milestone-template.mdx", "title": "Milestone <ID> — <One-line scope>", "description": "<Short paragraph of intent>", "created": "<YYYY-MM-DD>", "version": "0.0.0", "status": "Draft", "tags": ["milestone"], "authors": []}, {"@id": "spec----docs-tech-specs-process-templates-process-improvement-mdx-process-improvement-template", "@type": "Specification", "filePath": "../docs/tech-specs/process/templates/process-improvement.mdx", "title": "Process Improvement Template", "description": "Streamlined template for capturing process improvements and lessons learned", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["template", "process-improvement", "lessons-learned", "agent-ready"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-templates-requirement-checklist-mdx-requirement-checklist-template", "@type": "Specification", "filePath": "../docs/tech-specs/process/templates/requirement-checklist.mdx", "title": "Requirement Checklist Template", "description": "Streamlined pre-implementation validation checklist", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["template", "checklist", "requirements", "agent-ready"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-templates-work-log-template-mdx-work-log-template", "@type": "Milestone", "filePath": "../docs/tech-specs/process/templates/work-log-template.mdx", "title": "Work Log Template", "description": "Streamlined template for milestone implementation work logs", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["template", "work-log", "milestone", "agent-ready"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-spec-checklist-mdx-spec-checklist", "@type": "Specification", "filePath": "../docs/tech-specs/spec_checklist.mdx", "title": "Spec Checklist", "description": "Quick reference for milestone specification validation requirements", "version": "2.1.0", "status": "Living", "tags": ["checklist", "validation", "reference"]}, {"@id": "spec----docs-tech-specs-structure-mdx-repository-structure---conventions", "@type": "Specification", "filePath": "../docs/tech-specs/structure.mdx", "title": "Repository Structure & Conventions", "description": "Living guideline—update with every structural PR. Single source of truth for project structure.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.2.0", "status": "Living", "tags": ["structure"], "authors": ["nitishMeh<PERSON><PERSON>"]}]}