{"timestamp": "2025-06-04T06:18:39.729Z", "summary": {"nodesAdded": 0, "nodesUpdated": 2, "nodesMarkedStale": 0, "edgesAdded": 0, "edgesUpdated": 2, "edgesMarkedStale": 0}, "coverage": [], "errors": [{"message": "Multiple @implements annotations found for M2#AuditSchema", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "File name \"auditReport.ts\" doesn't match component \"AuditSchema\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "Duplicate @implements annotation for M2#AuditSchema", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "File name \"auditReport.ts\" doesn't match component \"AuditSchema\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}]}