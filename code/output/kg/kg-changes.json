{"timestamp": "2025-06-03T04:43:15.328Z", "summary": {"nodesAdded": 14, "nodesUpdated": 5, "nodesMarkedStale": 0, "edgesAdded": 8, "edgesUpdated": 3, "edgesMarkedStale": 0}, "coverage": [{"milestoneId": "Mm0", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}, {"milestoneId": "<PERSON><PERSON><PERSON><PERSON>", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}, {"milestoneId": "Mm1", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}, {"milestoneId": "Mm2", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}, {"milestoneId": "Mm3", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}, {"milestoneId": "Mtest", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}, {"milestoneId": "Mimplementation", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}, {"milestoneId": "Mtemplate", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-03T04:43:15.325Z"}], "errors": [{"message": "Multiple @implements annotations found for M2#AuditReportGeneration", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "File name \"auditReport.ts\" doesn't match component \"AuditReportGeneration\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "Duplicate @implements annotation for M2#AuditReportGeneration", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "File name \"auditReport.ts\" doesn't match component \"AuditReportGeneration\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "Function name \"generateAuditReport\" doesn't match component \"SchemaValidation\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "File name \"auditReport.ts\" doesn't match component \"SchemaValidation\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "Multiple @implements annotations found for M2#AuditCoverage", "severity": "warning", "file": "code/packages/kg-audit-lib/src/coverage.ts", "line": 1}, {"message": "File name \"coverage.ts\" doesn't match component \"AuditCoverage\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/coverage.ts", "line": 1}, {"message": "File name \"coverage.ts\" doesn't match component \"CoverageCalculation\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/coverage.ts", "line": 1}, {"message": "Duplicate @implements annotation for M2#AuditCoverage", "severity": "warning", "file": "code/packages/kg-audit-lib/src/coverage.ts", "line": 1}, {"message": "File name \"coverage.ts\" doesn't match component \"AuditCoverage\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/coverage.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-audit-lib/src/index.ts", "line": 1}, {"message": "File name \"index.ts\" doesn't match component \"AuditLibrary\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/index.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-audit-lib/src/types.ts", "line": 1}, {"message": "File name \"types.ts\" doesn't match component \"AuditTypes\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/types.ts", "line": 1}, {"message": "Multiple @implements annotations found for M2#UnknownEdgeDetection", "severity": "warning", "file": "code/packages/kg-audit-lib/src/unknownEdges.ts", "line": 1}, {"message": "File name \"unknownEdges.ts\" doesn't match component \"UnknownEdgeDetection\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/unknownEdges.ts", "line": 1}, {"message": "Duplicate @implements annotation for M2#UnknownEdgeDetection", "severity": "warning", "file": "code/packages/kg-audit-lib/src/unknownEdges.ts", "line": 1}, {"message": "File name \"unknownEdges.ts\" doesn't match component \"UnknownEdgeDetection\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/unknownEdges.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-cli/src/audit-kg.ts", "line": 1}, {"message": "File name \"audit-kg.ts\" doesn't match component \"CLIIntegration\"", "severity": "warning", "file": "code/packages/kg-cli/src/audit-kg.ts", "line": 1}]}