
> workflow-mapper@ audit-kg /Users/<USER>/tmp/kloudi-swe-agent/code
> node packages/kg-cli/dist/audit-kg.js "--" "--since" "origin/main" "--format" "json" "--fail-under" "0.5"

🔍 Starting knowledge graph audit...
📊 Loaded 14 nodes and 8 edges
📋 Found 0 milestones
🔍 Detecting unknown edges...
📈 Calculating coverage metrics...
✅ Validating audit report...

📊 Knowledge Graph Audit Results
================================

📅 Generated: 6/4/2025, 9:50:27 AM
🔍 Files Scanned: 14
📈 Git Reference: HEAD

🔗 Edge Summary:
  • Implements: 8
  • Workflow Calls: 0
  • Dependencies: 0
  • Total: 8

🎯 Milestone Coverage:

⚠️  Unknown Edges Found: 8
  • M1.2: 1 unknown edges
  • M2: 7 unknown edges

  Examples:
    - implements: function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph → component:M1.2#GraphUpdateCore (stale_spec)
    - implements: function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport → component:M2#AuditSchema (stale_spec)
    - implements: function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage → component:M2#Coverage (stale_spec)
    ... and 5 more

⚡ Performance:
  • Duration: 3ms
  • Files Processed: 14
  • Edges Analyzed: 8

✅ Audit completed successfully!
