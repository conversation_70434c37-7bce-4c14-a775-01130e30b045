/**
 * @fileoverview Tests for unknown edge detection functionality
 * @implements milestone-M2#UnknownEdgeDetection
 */

import {
  detectUnknownEdges,
  groupUnknownEdgesByMilestone
} from '../src/unknownEdges.js';
import type { KnowledgeGraphNode, KnowledgeGraphEdge } from '@workflow-mapper/kg-sync-lib';
import type { UnknownEdge } from '../src/types.js';

// Define AuditEdge to match the internal interface in unknownEdges.ts
interface AuditEdge extends Omit<KnowledgeGraphEdge, '@type'> {
  '@type': 'implements' | 'dependsOn' | 'contains' | 'workflow_calls';
  filePath?: string;
  lineNumber?: number;
}

describe('Unknown Edge Detection', () => {
  const createNode = (
    id: string,
    type: string,
    additionalProps: any = {}
  ): KnowledgeGraphNode => ({
    '@id': id,
    '@type': type,
    ...additionalProps,
  });

  const createEdge = (
    type: string,
    source: string,
    target: string,
    filePath?: string,
    lineNumber?: number
  ): AuditEdge => ({
    '@id': `${type}:${source}->${target}`,
    '@type': type as any,
    source,
    target,
    confidence: 1.0,
    lastVerified: new Date().toISOString(),
    filePath,
    lineNumber,
  });

  describe('detectUnknownEdges', () => {
    describe('workflow_calls edge detection', () => {
      it('should detect missing workflow call targets', () => {
        const nodes = [
          createNode('function:src/test.ts#existingFunction', 'Function', {
            functionName: 'existingFunction'
          }),
        ];

        const edges = [
          createEdge('workflow_calls', 'function:src/caller.ts#caller', 'existingFunction'),
          createEdge('workflow_calls', 'function:src/caller.ts#caller', 'missingFunction'),
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          type: 'workflow_calls',
          source: 'function:src/caller.ts#caller',
          target: 'missingFunction',
          confidence: 0.2,
          reason: 'missing_target',
          filePath: undefined,
          lineNumber: undefined,
        });
      });

      it('should handle qualified function names', () => {
        const nodes = [
          createNode('function:src/utils.ts#helper', 'Function', {
            functionName: 'helper'
          }),
        ];

        const edges = [
          createEdge('workflow_calls', 'caller', 'module:utils#helper'),
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(0); // Should match by extracted function name
      });

      it('should handle direct ID matches', () => {
        const nodes = [
          createNode('function:src/test.ts#directMatch', 'Function'),
        ];

        const edges = [
          createEdge('workflow_calls', 'caller', 'function:src/test.ts#directMatch'),
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(0); // Should match by direct ID
      });

      it('should handle parse errors gracefully', () => {
        const nodes = [
          createNode('function:src/test.ts#validFunction', 'Function', {
            functionName: 'validFunction'
          }),
        ];

        // Create edge with empty target that will be treated as missing
        const edges = [
          createEdge('workflow_calls', 'caller', ''), // Empty target
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1);
        expect(result[0]?.reason).toBe('missing_target');
        expect(result[0]?.confidence).toBe(0.2);
      });
    });

    describe('implements edge detection', () => {
      it('should detect stale milestone specs', () => {
        const nodes = [
          createNode('milestone:M1.1', 'Milestone', {
            milestoneId: 'M1.1',
            content: 'Valid milestone content with ComponentA'
          }),
          // M1.2 milestone is missing
        ];

        const edges = [
          createEdge('implements', 'function:src/test.ts#func1', 'milestone-M1.1#ComponentA'),
          createEdge('implements', 'function:src/test.ts#func2', 'milestone-M1.2#ComponentB'),
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          type: 'implements',
          source: 'function:src/test.ts#func2',
          target: 'milestone-M1.2#ComponentB',
          confidence: 0.2,
          reason: 'stale_spec',
          filePath: undefined,
          lineNumber: undefined,
        });
      });

      it('should detect missing components in existing specs', () => {
        const nodes = [
          createNode('milestone:M1.1', 'Milestone', {
            milestoneId: 'M1.1',
            content: 'Milestone content with ComponentA only'
          }),
        ];

        const edges = [
          createEdge('implements', 'function:src/test.ts#func1', 'milestone-M1.1#ComponentA'),
          createEdge('implements', 'function:src/test.ts#func2', 'milestone-M1.1#MissingComponent'),
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1);
        expect(result[0]?.reason).toBe('missing_target');
        expect(result[0]?.target).toBe('milestone-M1.1#MissingComponent');
      });

      it('should handle various milestone ID formats', () => {
        const nodes = [
          createNode('milestone:M1.1', 'Milestone', {
            milestoneId: 'M1.1',
            content: 'Content with ComponentA'
          }),
          createNode('milestone:M2.3', 'milestone', { // lowercase type
            milestoneId: 'M2.3',
            content: 'Content with ComponentB'
          }),
        ];

        const edges = [
          createEdge('implements', 'func1', 'milestone-M1.1#ComponentA'),
          createEdge('implements', 'func2', 'component:M2.3#ComponentB'),
          createEdge('implements', 'func3', 'M1.5#ComponentC'), // Direct format
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1); // Only M1.5 should be missing
        expect(result[0]?.target).toBe('M1.5#ComponentC');
        expect(result[0]?.reason).toBe('stale_spec');
      });

      it('should handle component detection with various patterns', () => {
        const nodes = [
          createNode('milestone:M1.1', 'Milestone', {
            milestoneId: 'M1.1',
            content: `
              ## ComponentA
              This is a component.

              ## "ComponentB"
              Quoted component name.

              ComponentC: implementation details

              @implements ComponentD
            `
          }),
        ];

        const edges = [
          createEdge('implements', 'func1', 'milestone-M1.1#ComponentA'),
          createEdge('implements', 'func2', 'milestone-M1.1#ComponentB'),
          createEdge('implements', 'func3', 'milestone-M1.1#ComponentC'),
          createEdge('implements', 'func4', 'milestone-M1.1#ComponentD'),
          createEdge('implements', 'func5', 'milestone-M1.1#ComponentE'), // Missing
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1);
        expect(result[0]?.target).toBe('milestone-M1.1#ComponentE');
        expect(result[0]?.reason).toBe('missing_target');
      });

      it('should handle parse errors in implements edges', () => {
        const nodes = [
          createNode('milestone:M1.1', 'Milestone', {
            milestoneId: 'M1.1',
            content: 'Valid content'
          }),
        ];

        const edges = [
          createEdge('implements', 'func1', 'malformed-target-format'),
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1);
        expect(result[0]?.reason).toBe('stale_spec');
        expect(result[0]?.confidence).toBe(0.2);
      });
    });

    describe('error handling', () => {
      it('should handle empty inputs gracefully', () => {
        const result = detectUnknownEdges([], []);
        expect(result).toHaveLength(0);
      });

      it('should handle null/undefined inputs', () => {
        const result = detectUnknownEdges(null as any, undefined as any);
        expect(result).toHaveLength(0);
      });

      it('should continue processing despite individual edge errors', () => {
        const nodes = [
          createNode('function:src/test.ts#validFunction', 'Function', {
            functionName: 'validFunction'
          }),
        ];

        const edges = [
          createEdge('workflow_calls', 'caller', ''), // Empty target - missing
          createEdge('workflow_calls', 'caller', 'validFunction'), // Should work
          createEdge('workflow_calls', 'caller', 'missingFunction'), // Should detect as unknown
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(2); // Empty target + missing target
        expect(result.some(r => r.reason === 'missing_target')).toBe(true);
        expect(result.every(r => r.reason === 'missing_target')).toBe(true);
      });

      it('should handle invalid node structures gracefully', () => {
        // Test with invalid node structure
        const invalidNodes = [] as any;
        const validEdges = [createEdge('workflow_calls', 'caller', 'target')];

        const result = detectUnknownEdges(invalidNodes, validEdges);

        expect(result).toHaveLength(1); // Should detect missing target
        expect(result[0]?.reason).toBe('missing_target');
      });
    });

    describe('edge cases', () => {
      it('should handle edges with file path and line number', () => {
        const nodes: KnowledgeGraphNode[] = [];
        const edges = [
          createEdge('workflow_calls', 'caller', 'missingFunction', 'src/test.ts', 42),
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(1);
        expect(result[0]?.filePath).toBe('src/test.ts');
        expect(result[0]?.lineNumber).toBe(42);
      });

      it('should handle mixed edge types', () => {
        const nodes: KnowledgeGraphNode[] = [];
        const edges = [
          createEdge('workflow_calls', 'caller', 'missingFunction'),
          createEdge('implements', 'func', 'milestone-M999#MissingComponent'),
          createEdge('dependsOn', 'comp1', 'comp2'), // Should be ignored
        ];

        const result = detectUnknownEdges(nodes, edges);

        expect(result).toHaveLength(2); // Only workflow_calls and implements
        expect(result.some(r => r.type === 'workflow_calls')).toBe(true);
        expect(result.some(r => r.type === 'implements')).toBe(true);
      });
    });
  });

  describe('groupUnknownEdgesByMilestone', () => {
    const createUnknownEdge = (
      type: string,
      target: string,
      reason: string = 'missing_target'
    ): UnknownEdge => ({
      type: type as any,
      source: 'source',
      target,
      confidence: 0.2,
      reason: reason as any,
    });

    it('should group edges by milestone ID', () => {
      const unknownEdges = [
        createUnknownEdge('implements', 'milestone-M1.1#ComponentA'),
        createUnknownEdge('implements', 'milestone-M1.1#ComponentB'),
        createUnknownEdge('implements', 'milestone-M1.2#ComponentC'),
        createUnknownEdge('workflow_calls', 'component:M1.3#ComponentD'),
      ];

      const result = groupUnknownEdgesByMilestone(unknownEdges);

      expect(result.get('M1.1')).toBe(2);
      expect(result.get('M1.2')).toBe(1);
      expect(result.get('M1.3')).toBe(1);
    });

    it('should handle various milestone ID patterns', () => {
      const unknownEdges = [
        createUnknownEdge('implements', 'milestone-M1.1#Component'),
        createUnknownEdge('implements', 'component:M1.2#Component'),
        createUnknownEdge('implements', 'milestone-M1.3#Component'), // Use milestone- prefix
        createUnknownEdge('implements', 'invalid-format'),
      ];

      const result = groupUnknownEdgesByMilestone(unknownEdges);

      expect(result.get('M1.1')).toBe(1);
      expect(result.get('M1.2')).toBe(1);
      expect(result.get('M1.3')).toBe(1);
      expect(result.get('unknown')).toBe(1); // Invalid format goes to 'unknown'
    });

    it('should handle empty input', () => {
      const result = groupUnknownEdgesByMilestone([]);
      expect(result.size).toBe(0);
    });

    it('should accumulate counts for same milestone', () => {
      const unknownEdges = [
        createUnknownEdge('implements', 'milestone-M1.1#ComponentA'),
        createUnknownEdge('workflow_calls', 'milestone-M1.1#ComponentB'),
        createUnknownEdge('implements', 'milestone-M1.1#ComponentC'),
      ];

      const result = groupUnknownEdgesByMilestone(unknownEdges);

      expect(result.get('M1.1')).toBe(3);
    });

    it('should handle targets without milestone patterns', () => {
      const unknownEdges = [
        createUnknownEdge('workflow_calls', 'someFunction'),
        createUnknownEdge('workflow_calls', 'anotherFunction'),
      ];

      const result = groupUnknownEdgesByMilestone(unknownEdges);

      expect(result.get('unknown')).toBe(2);
    });
  });
});
