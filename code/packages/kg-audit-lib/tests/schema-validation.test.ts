/**
 * @fileoverview Basic schema validation tests for Task 01a
 * @implements milestone-M2#SchemaValidation
 */

import { validateAuditReport } from '../src/auditReport.js';
import type { AuditReport } from '../src/types.js';
import { readFileSync } from 'fs';

// Mock kg-sync-lib to avoid ES module issues
jest.mock('@workflow-mapper/kg-sync-lib', () => ({
  calculateCoverage: jest.fn(),
}));

// Mock fs for schema loading tests
jest.mock('fs');
const mockReadFileSync = readFileSync as jest.MockedFunction<typeof readFileSync>;

describe('Schema Validation', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock successful schema loading by default
    mockReadFileSync.mockImplementation((path: any) => {
      if (path.includes('kg-audit.schema.json')) {
        return JSON.stringify({
          "$id": "https://workflow-mapper.dev/schemas/kg-audit.schema.json",
          "title": "Knowledge-Graph Audit Report",
          "type": "object",
          "required": ["summary", "milestones", "unknownEdges", "performance"],
          "properties": {
            "summary": {
              "type": "object",
              "required": ["generatedAt", "edgeTotals", "gitRef", "filesScanned"],
              "properties": {
                "generatedAt": { "type": "string", "format": "date-time" },
                "edgeTotals": {
                  "type": "object",
                  "properties": {
                    "implements": { "type": "integer", "minimum": 0 },
                    "workflow_calls": { "type": "integer", "minimum": 0 },
                    "dependsOn": { "type": "integer", "minimum": 0 },
                    "total": { "type": "integer", "minimum": 0 }
                  }
                },
                "gitRef": { "type": "string" },
                "filesScanned": { "type": "integer", "minimum": 0 }
              }
            },
            "milestones": { "type": "array", "items": { "type": "object" } },
            "unknownEdges": { "type": "array", "items": { "type": "object" } },
            "performance": { "type": "object" }
          }
        });
      }
      throw new Error('File not found');
    });
  });

  it('should validate a valid audit report', async () => {
    const validReport: AuditReport = {
      summary: {
        generatedAt: new Date().toISOString(),
        edgeTotals: {
          implements: 5,
          workflow_calls: 3,
          dependsOn: 2,
          total: 10,
        },
        gitRef: 'HEAD',
        filesScanned: 100,
      },
      milestones: [
        {
          milestoneId: 'M1.1',
          coverage: 0.85,
          confidence: 0.92,
          lastUpdated: new Date().toISOString(),
          totalComponents: 10,
          implementedComponents: 8,
          unknownEdgeCount: 1,
          auditTimestamp: new Date().toISOString(),
          components: {
            total: 10,
            implemented: 8,
            stale: 1,
          },
        },
      ],
      unknownEdges: [
        {
          type: 'workflow_calls',
          source: 'function1',
          target: 'missing_function',
          confidence: 0.2,
          reason: 'missing_target',
          filePath: 'src/test.ts',
          lineNumber: 42,
        },
      ],
      performance: {
        durationMs: 1500,
        filesProcessed: 100,
        edgesAnalyzed: 250,
        cacheHits: 50,
      },
    };

    const result = await validateAuditReport(validReport);
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it('should reject an invalid audit report', async () => {
    const invalidReport = {
      summary: {
        // Missing required fields
        generatedAt: 'invalid-date',
        edgeTotals: {
          implements: 0,
          workflow_calls: 0,
          dependsOn: 0,
          total: 0,
        },
        gitRef: 'HEAD',
        filesScanned: 0,
      },
      milestones: [],
      unknownEdges: [],
      performance: {
        durationMs: 100,
        filesProcessed: 0,
        edgesAnalyzed: 0,
      },
    } as AuditReport;

    const result = await validateAuditReport(invalidReport);
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });

  it('should handle schema loading errors gracefully', async () => {
    // Test with process.cwd() pointing to wrong location
    try {
      // Mock process.cwd to return invalid path
      jest.spyOn(process, 'cwd').mockReturnValue('/invalid/path');

      const validReport: AuditReport = {
        summary: {
          generatedAt: new Date().toISOString(),
          edgeTotals: {
            implements: 0,
            workflow_calls: 0,
            dependsOn: 0,
            total: 0,
          },
          gitRef: 'HEAD',
          filesScanned: 0,
        },
        milestones: [],
        unknownEdges: [],
        performance: {
          durationMs: 100,
          filesProcessed: 0,
          edgesAnalyzed: 0,
        },
      };

      const result = await validateAuditReport(validReport);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Schema validation failed');
    } finally {
      // Restore all mocks
      jest.restoreAllMocks();
    }
  });
});
