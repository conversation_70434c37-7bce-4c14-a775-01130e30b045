/**
 * @fileoverview Audit report generation and validation
 */

import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { readFileSync } from 'fs';
import { join } from 'path';
import type {
  KnowledgeGraphNode,
  KnowledgeGraphEdge,
} from '@workflow-mapper/kg-sync-lib';
import type {
  AuditReport,
  AuditOptions,
  AuditValidationResult,
} from './types.js';
import { calculateAuditCoverage } from './coverage.js';
import { detectUnknownEdges } from './unknownEdges.js';

/**
 * Generate comprehensive audit report
 * @implements milestone-M2#AuditSchema
 * @param nodes - Knowledge graph nodes
 * @param edges - Knowledge graph edges
 * @param options - Audit options
 * @returns Complete audit report with validation status
 */
export async function generateAuditReport(
  nodes: KnowledgeGraphNode[],
  edges: KnowledgeGraphEdge[],
  options: AuditOptions = {}
): Promise<{ report: AuditReport; isValid: boolean; errors: string[]; }> {
  const startTime = Date.now();

  // 1. Calculate edge totals
  const edgeTotals = calculateEdgeTotals(edges);

  // 2. Detect unknown edges
  const unknownEdges = detectUnknownEdges(nodes, edges);

  // 3. Calculate milestone metrics
  const milestones = calculateMilestoneMetrics(
    nodes,
    edges,
    unknownEdges.length
  );

  // 4. Build report
  const report: AuditReport = {
    summary: {
      generatedAt: new Date().toISOString(),
      edgeTotals,
      gitRef: options.since || 'HEAD',
      filesScanned: nodes.filter((n) => n['@type'] === 'Function').length,
    },
    milestones,
    unknownEdges,
    performance: {
      durationMs: Date.now() - startTime,
      filesProcessed: nodes.filter((n) => n.filePath).length,
      edgesAnalyzed: edges.length,
      cacheHits: 0,
    },
  };

  // 5. Validate against schema (placeholder - will implement with schema)
  const { isValid, errors } = await validateAuditReport(report);

  return { report, isValid, errors };
}

/**
 * Validate audit report against JSON schema
 * @implements milestone-M2#AuditSchema
 * @param report - Audit report to validate
 * @returns Validation result
 */
export async function validateAuditReport(
  report: AuditReport
): Promise<AuditValidationResult> {
  try {
    // Load schema from output/schema/kg-audit.schema.json (relative to code directory)
    // Handle both package-level and root-level execution contexts
    const possiblePaths = [
      join(process.cwd(), 'output/schema/kg-audit.schema.json'), // From root
      join(process.cwd(), '../../output/schema/kg-audit.schema.json'), // From package
      join(__dirname, '../../../output/schema/kg-audit.schema.json'), // From built package
    ];

    let schemaPath: string | null = null;
    for (const path of possiblePaths) {
      try {
        if (readFileSync(path, 'utf-8')) {
          schemaPath = path;
          break;
        }
      } catch {
        // Continue to next path
      }
    }

    if (!schemaPath) {
      throw new Error(`Schema file not found. Tried paths: ${possiblePaths.join(', ')}`);
    }
    const schemaContent = readFileSync(schemaPath, 'utf-8');
    const schema = JSON.parse(schemaContent);

    // Set up AJV with formats and draft 2020-12 support
    const ajv = new Ajv({
      allErrors: true,
      strict: false, // Allow draft 2020-12 features
      validateFormats: true
    });
    addFormats(ajv);

    // Compile and validate
    const validate = ajv.compile(schema);
    const isValid = validate(report);

    const errors =
      validate.errors?.map(
        (error) => `${error.instancePath || 'root'}: ${error.message}`
      ) || [];

    return {
      isValid,
      errors,
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [
        `Schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      ],
    };
  }
}

/**
 * Calculate edge totals by type
 */
function calculateEdgeTotals(edges: KnowledgeGraphEdge[]) {
  const totals = {
    implements: 0,
    workflow_calls: 0,
    dependsOn: 0,
    total: 0,
  };

  for (const edge of edges) {
    const edgeType = edge['@type'];
    if (edgeType in totals) {
      totals[edgeType as keyof typeof totals]++;
    }
    totals.total++;
  }

  return totals;
}

/**
 * Calculate milestone metrics using audit coverage
 */
function calculateMilestoneMetrics(
  nodes: KnowledgeGraphNode[],
  edges: KnowledgeGraphEdge[],
  unknownEdgeCount: number
) {
  const milestoneNodes = nodes.filter((node) => node['@type'] === 'Milestone');
  const metrics = [];

  for (const milestone of milestoneNodes) {
    const milestoneFullId = String(milestone['@id']);
    const milestoneId = milestoneFullId.replace('milestone:', '');

    const implementsEdges = edges.filter(
      (edge) =>
        edge['@type'] === 'implements' && edge.target.includes(milestoneFullId)
    );

    const totalComponents = extractComponentCount(milestone);
    const auditCoverage = calculateAuditCoverage(
      milestoneId,
      implementsEdges,
      totalComponents,
      unknownEdgeCount
    );

    metrics.push(auditCoverage);
  }

  return metrics;
}

/**
 * Extract component count from milestone specification
 */
function extractComponentCount(milestone: KnowledgeGraphNode): number {
  const content = String(milestone.content || '');

  // Look for task breakdown, deliverables, etc.
  const taskMatches = content.match(/### Task \d+:/g) || [];
  const deliverableMatches =
    content.match(/\| [A-Za-z_][A-Za-z0-9_]* \|/g) || [];

  return Math.max(taskMatches.length, deliverableMatches.length, 1);
}
