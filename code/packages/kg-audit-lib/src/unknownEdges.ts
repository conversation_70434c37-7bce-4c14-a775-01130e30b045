/**
 * @fileoverview Unknown edge detection for knowledge graph audit
 * @implements milestone-M2#UnknownEdges
 */

import type {
  KnowledgeGraphNode,
  KnowledgeGraphEdge,
} from '@workflow-mapper/kg-sync-lib';
import type { UnknownEdge } from './types.js';

// Extended edge interface for audit functionality
interface AuditEdge extends Omit<KnowledgeGraphEdge, '@type'> {
  '@type': 'implements' | 'dependsOn' | 'contains' | 'workflow_calls';
  filePath?: string;
  lineNumber?: number;
}

/**
 * Detect unknown edges in the knowledge graph
 * @implements milestone-M2#UnknownEdges
 * @param nodes - Knowledge graph nodes
 * @param edges - Knowledge graph edges
 * @returns Array of unknown edges detected
 */
export function detectUnknownEdges(
  nodes: KnowledgeGraphNode[],
  edges: AuditEdge[]
): UnknownEdge[] {
  const unknownEdges: UnknownEdge[] = [];

  try {
    // 1. Check workflow_calls for missing targets
    const workflowCallEdges = edges.filter(
      (e) => e['@type'] === 'workflow_calls'
    );

    for (const edge of workflowCallEdges) {
      try {
        const targetExists = checkWorkflowTargetExists(edge.target, nodes);
        if (!targetExists) {
          unknownEdges.push({
            type: 'workflow_calls',
            source: edge.source,
            target: edge.target,
            confidence: 0.2,
            reason: 'missing_target',
            filePath: edge.filePath,
            lineNumber: edge.lineNumber,
          });
        }
      } catch (error) {
        // Handle parse errors gracefully
        unknownEdges.push({
          type: 'workflow_calls',
          source: edge.source,
          target: edge.target,
          confidence: 0.1,
          reason: 'parse_error',
          filePath: edge.filePath,
          lineNumber: edge.lineNumber,
        });
      }
    }

    // 2. Check implements edges for stale specs
    const implementsEdges = edges.filter((e) => e['@type'] === 'implements');

    for (const edge of implementsEdges) {
      try {
        const specExists = checkSpecExists(edge.target, nodes);
        const componentExists = checkComponentExists(edge.target, nodes);

        if (!specExists) {
          unknownEdges.push({
            type: 'implements',
            source: edge.source,
            target: edge.target,
            confidence: 0.2,
            reason: 'stale_spec',
            filePath: edge.filePath,
            lineNumber: edge.lineNumber,
          });
        } else if (!componentExists) {
          unknownEdges.push({
            type: 'implements',
            source: edge.source,
            target: edge.target,
            confidence: 0.2,
            reason: 'missing_target',
            filePath: edge.filePath,
            lineNumber: edge.lineNumber,
          });
        }
      } catch (error) {
        // Handle parse errors gracefully
        unknownEdges.push({
          type: 'implements',
          source: edge.source,
          target: edge.target,
          confidence: 0.1,
          reason: 'parse_error',
          filePath: edge.filePath,
          lineNumber: edge.lineNumber,
        });
      }
    }
  } catch (error) {
    // Log error but don't fail the entire detection process
    console.warn('Error during unknown edge detection:', error);
  }

  return unknownEdges;
}

/**
 * Check if workflow call target exists in nodes
 * Enhanced with multiple matching strategies
 */
function checkWorkflowTargetExists(
  target: string,
  nodes: KnowledgeGraphNode[]
): boolean {
  if (!target || !nodes || nodes.length === 0) {
    return false;
  }

  return nodes.some((node) => {
    // Direct ID match
    if (node['@id'] === target) {
      return true;
    }

    // Function name match
    if (node.functionName && node.functionName === target) {
      return true;
    }

    // Extract function name from target if it's a qualified name
    const functionMatch = target.match(/([^:]+)$/);
    if (functionMatch && node.functionName === functionMatch[1]) {
      return true;
    }

    // Check if target matches any node type pattern
    if (
      node['@type'] === 'Function' &&
      target.includes(node.functionName || '')
    ) {
      return true;
    }

    return false;
  });
}

/**
 * Check if milestone spec exists
 * Enhanced with better pattern matching
 */
function checkSpecExists(target: string, nodes: KnowledgeGraphNode[]): boolean {
  if (!target || !nodes || nodes.length === 0) {
    return false;
  }

  // Extract milestone ID from various target formats
  const milestonePatterns = [
    /milestone-([^#]+)/, // milestone-M1.2#Component
    /component:([^#]+)#/, // component:M1.2#Component
    /M(\d+(?:\.\d+)*)/, // Direct milestone ID like M1.2
  ];

  let milestoneId: string | null = null;

  for (const pattern of milestonePatterns) {
    const match = target.match(pattern);
    if (match && match[1]) {
      milestoneId = match[1];
      break;
    }
  }

  if (!milestoneId) return false;

  return nodes.some((node) => {
    const nodeId = node['@id'];
    const nodeType = node['@type'];

    return (
      (nodeType === 'Milestone' || nodeType === 'milestone') &&
      nodeId &&
      (String(nodeId).includes(milestoneId) ||
        String(nodeId).includes(`M${milestoneId}`) ||
        (node.milestoneId && String(node.milestoneId).includes(milestoneId)))
    );
  });
}

/**
 * Check if component exists in milestone spec
 * Enhanced with multiple component detection strategies
 */
function checkComponentExists(
  target: string,
  nodes: KnowledgeGraphNode[]
): boolean {
  if (!target || !nodes || nodes.length === 0) {
    return false;
  }

  // Extract milestone ID and component name from various formats
  const componentPatterns = [
    /milestone-([^#]+)#(.+)/, // milestone-M1.2#ComponentName
    /component:([^#]+)#(.+)/, // component:M1.2#ComponentName
    /([^#]+)#(.+)/, // M1.2#ComponentName
  ];

  let milestoneId: string | null = null;
  let componentName: string | null = null;

  for (const pattern of componentPatterns) {
    const match = target.match(pattern);
    if (match && match[1] && match[2]) {
      milestoneId = match[1];
      componentName = match[2];
      break;
    }
  }

  if (!milestoneId || !componentName) return false;

  // Find the milestone node
  const milestoneNode = nodes.find((node) => {
    const nodeId = node['@id'];
    const nodeType = node['@type'];

    return (
      (nodeType === 'Milestone' || nodeType === 'milestone') &&
      nodeId &&
      (String(nodeId).includes(milestoneId) ||
        String(nodeId).includes(`M${milestoneId}`) ||
        (node.milestoneId && String(node.milestoneId).includes(milestoneId)))
    );
  });

  if (!milestoneNode) return false;

  // Check if component is mentioned in milestone content using multiple strategies
  const content = milestoneNode.content;
  if (!content) return false;

  const contentStr = String(content);

  // Strategy 1: Direct component name match
  if (contentStr.includes(componentName)) {
    return true;
  }

  // Strategy 2: Check for component patterns in content
  const componentPatternChecks = [
    new RegExp(`\\b${componentName}\\b`, 'i'), // Word boundary match
    new RegExp(`"${componentName}"`, 'i'), // Quoted component name
    new RegExp(`'${componentName}'`, 'i'), // Single quoted
    new RegExp(`\`${componentName}\``, 'i'), // Backtick quoted
    new RegExp(`${componentName}\\s*:`, 'i'), // Component as key
    new RegExp(`implements.*${componentName}`, 'i'), // Implements annotation
  ];

  return componentPatternChecks.some((pattern) => pattern.test(contentStr));
}

/**
 * Group unknown edges by milestone for reporting
 * @param unknownEdges - Array of unknown edges
 * @returns Map of milestone ID to unknown edge count
 */
export function groupUnknownEdgesByMilestone(
  unknownEdges: UnknownEdge[]
): Map<string, number> {
  const milestoneGroups = new Map<string, number>();

  for (const edge of unknownEdges) {
    // Extract milestone ID from target
    const milestonePatterns = [
      /milestone-([^#]+)/, // milestone-M1.2#Component
      /component:([^#]+)#/, // component:M1.2#Component
      /M(\d+(?:\.\d+)*)/, // Direct milestone ID like M1.2
    ];

    let milestoneId: string | null = null;

    for (const pattern of milestonePatterns) {
      const match = edge.target.match(pattern);
      if (match && match[1]) {
        milestoneId = match[1];
        break;
      }
    }

    if (milestoneId) {
      const currentCount = milestoneGroups.get(milestoneId) || 0;
      milestoneGroups.set(milestoneId, currentCount + 1);
    } else {
      // Group under 'unknown' if we can't determine milestone
      const currentCount = milestoneGroups.get('unknown') || 0;
      milestoneGroups.set('unknown', currentCount + 1);
    }
  }

  return milestoneGroups;
}
