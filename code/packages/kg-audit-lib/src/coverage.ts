/**
 * @fileoverview Coverage calculation extensions for audit functionality
 * @implements milestone-M2#AuditCoverage
 */

import {
  calculateCoverage,
  type KnowledgeGraphEdge,
} from '@workflow-mapper/kg-sync-lib';
import type { AuditCoverage } from './types.js';

/**
 * Calculate audit-specific coverage metrics extending kg-sync-lib functionality
 * @implements milestone-M2#CoverageCalculation
 * @param milestoneId - Milestone identifier
 * @param implementsEdges - Implements edges for the milestone
 * @param totalComponents - Total number of components in milestone
 * @param unknownEdgeCount - Number of unknown edges detected
 * @returns Extended coverage metrics with audit information
 */
export function calculateAuditCoverage(
  milestoneId: string,
  implementsEdges: KnowledgeGraphEdge[],
  totalComponents: number,
  unknownEdgeCount: number = 0
): AuditCoverage {
  // Reuse existing coverage calculation (95% reuse)
  const baseCoverage = calculateCoverage(
    milestoneId,
    implementsEdges,
    totalComponents
  );

  // Calculate audit-specific metrics
  const staleComponents = calculateStaleComponents(
    implementsEdges,
    milestoneId
  );
  const auditTimestamp = new Date().toISOString();

  // Extend with audit-specific metrics
  const auditCoverage: AuditCoverage = {
    ...baseCoverage,
    unknownEdgeCount,
    auditTimestamp,
    components: {
      total: totalComponents,
      implemented: baseCoverage.implementedComponents,
      stale: staleComponents,
    },
  };

  return auditCoverage;
}

/**
 * Calculate the number of stale components for a milestone
 * @param implementsEdges - Implements edges for the milestone
 * @param milestoneId - Milestone identifier
 * @returns Number of unique stale components
 */
function calculateStaleComponents(
  implementsEdges: KnowledgeGraphEdge[],
  milestoneId: string
): number {
  // Filter edges that belong to this milestone and are stale (confidence <= 0.2)
  const staleEdges = implementsEdges.filter((edge) => {
    const belongsToMilestone = edge.target.includes(`${milestoneId}#`);
    const isStale = edge.confidence <= 0.2;
    return belongsToMilestone && isStale;
  });

  // Extract unique component names from stale edges
  const staleComponentNames = new Set<string>();

  for (const edge of staleEdges) {
    const componentMatch = edge.target.match(/^component:[^#]+#(.+)$/);
    if (componentMatch && componentMatch[1]) {
      staleComponentNames.add(componentMatch[1]);
    }
  }

  return staleComponentNames.size;
}

/**
 * Calculate audit coverage for all milestones in the knowledge graph
 * @implements milestone-M2#AuditCoverage
 * @param implementsEdges - All implements edges from the knowledge graph
 * @param milestoneComponents - Map of milestone ID to total component count
 * @param unknownEdgesByMilestone - Map of milestone ID to unknown edge count
 * @returns Array of audit coverage metrics for all milestones
 */
export function calculateAllMilestoneCoverage(
  implementsEdges: KnowledgeGraphEdge[],
  milestoneComponents: Map<string, number>,
  unknownEdgesByMilestone: Map<string, number> = new Map()
): AuditCoverage[] {
  const coverageResults: AuditCoverage[] = [];

  for (const [milestoneId, totalComponents] of milestoneComponents) {
    // Filter edges for this milestone
    const milestoneEdges = implementsEdges.filter((edge) =>
      edge.target.includes(`${milestoneId}#`)
    );

    const unknownEdgeCount = unknownEdgesByMilestone.get(milestoneId) || 0;

    const auditCoverage = calculateAuditCoverage(
      milestoneId,
      milestoneEdges,
      totalComponents,
      unknownEdgeCount
    );

    coverageResults.push(auditCoverage);
  }

  return coverageResults;
}
