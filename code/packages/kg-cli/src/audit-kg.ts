#!/usr/bin/env node

/**
 * @fileoverview audit-kg CLI command for knowledge graph audit and confidence analysis
 * @implements milestone-M2#CLIIntegration
 */

import { Command } from 'commander';
import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join, resolve } from 'path';
import {
  type KnowledgeGraphNode,
  type KnowledgeGraphEdge,
} from '@workflow-mapper/kg-sync-lib';
import {
  validateAuditReport,
  detectUnknownEdges,
  calculateAllMilestoneCoverage,
  groupUnknownEdgesByMilestone,
  type AuditReport,
} from '@workflow-mapper/kg-audit-lib';
import { parseSpecsDirectory } from '@workflow-mapper/spec-parser-lib';

interface AuditCliOptions {
  since?: string;
  format?: 'json' | 'pretty' | 'both';
  failUnder?: number;
  outputDir?: string;
  dryRun?: boolean;
  verbose?: boolean;
}

/**
 * Load existing knowledge graph from file
 * @param kgPath - Path to kg.jsonld file
 * @returns Knowledge graph data or empty structure
 */
function loadKnowledgeGraph(kgPath: string): {
  nodes: KnowledgeGraphNode[];
  edges: KnowledgeGraphEdge[];
} {
  if (!existsSync(kgPath)) {
    console.warn(`⚠️  Knowledge graph not found at ${kgPath}`);
    return { nodes: [], edges: [] };
  }

  try {
    const content = readFileSync(kgPath, 'utf-8');
    const graph = JSON.parse(content);

    // Extract nodes and edges from JSON-LD format
    const graphItems = graph['@graph'] || [];
    const nodes = graphItems.filter(
      (item: any) =>
        item['@type'] !== 'implements' &&
        item['@type'] !== 'dependsOn' &&
        item['@type'] !== 'workflow_calls'
    );
    const edges = graphItems.filter(
      (item: any) =>
        item['@type'] === 'implements' ||
        item['@type'] === 'dependsOn' ||
        item['@type'] === 'workflow_calls'
    );

    return { nodes, edges };
  } catch (error) {
    console.error(`❌ Failed to load knowledge graph: ${error}`);
    return { nodes: [], edges: [] };
  }
}

/**
 * Extract milestone component counts from specifications
 * @param specsDirectory - Directory containing specifications
 * @returns Map of milestone ID to component count
 */
function extractMilestoneComponents(
  specsDirectory: string
): Map<string, number> {
  const componentMap = new Map<string, number>();

  try {
    const parseResult = parseSpecsDirectory(specsDirectory);

    for (const spec of parseResult.specs) {
      // Extract milestone ID from file path or frontmatter
      const milestoneMatch = spec.filePath?.match(/milestone-([^/]+)\.mdx?$/);
      if (milestoneMatch && milestoneMatch[1]) {
        const milestoneId = milestoneMatch[1];

        // Count components in the specification content
        const content = spec.content || '';
        const componentMatches = content.match(/##\s+[^#\n]+/g) || [];
        const componentCount = Math.max(componentMatches.length, 1); // At least 1 component

        componentMap.set(milestoneId, componentCount);
      }
    }
  } catch (error: any) {
    console.warn(`⚠️  Failed to parse specifications: ${error}`);
  }

  return componentMap;
}

/**
 * Print audit results in pretty format
 * @param auditReport - Generated audit report
 */
function printAuditResults(auditReport: AuditReport): void {
  console.log('\n📊 Knowledge Graph Audit Results');
  console.log('================================');

  // Summary
  console.log(
    `\n📅 Generated: ${new Date(auditReport.summary.generatedAt).toLocaleString()}`
  );
  console.log(`🔍 Files Scanned: ${auditReport.summary.filesScanned}`);
  console.log(`📈 Git Reference: ${auditReport.summary.gitRef}`);

  // Edge totals
  const totals = auditReport.summary.edgeTotals;
  console.log(`\n🔗 Edge Summary:`);
  console.log(`  • Implements: ${totals.implements}`);
  console.log(`  • Workflow Calls: ${totals.workflow_calls}`);
  console.log(`  • Dependencies: ${totals.dependsOn}`);
  console.log(`  • Total: ${totals.total}`);

  // Milestone coverage
  console.log('\n🎯 Milestone Coverage:');
  for (const milestone of auditReport.milestones) {
    const coveragePercent = (milestone.coverage * 100).toFixed(1);
    const confidencePercent = (milestone.confidence * 100).toFixed(1);

    // Color coding based on coverage
    const coverageColor =
      milestone.coverage >= 0.75
        ? '🟢'
        : milestone.coverage >= 0.5
          ? '🟡'
          : '🔴';

    console.log(
      `  ${coverageColor} ${milestone.milestoneId}: ${coveragePercent}% coverage, ${confidencePercent}% confidence`
    );
    console.log(
      `     Components: ${milestone.components.implemented}/${milestone.components.total} (${milestone.components.stale} stale)`
    );
  }

  // Unknown edges
  if (auditReport.unknownEdges.length > 0) {
    console.log(
      `\n⚠️  Unknown Edges Found: ${auditReport.unknownEdges.length}`
    );

    const groupedEdges = groupUnknownEdgesByMilestone(auditReport.unknownEdges);
    for (const [milestoneId, count] of groupedEdges) {
      console.log(`  • ${milestoneId}: ${count} unknown edges`);
    }

    // Show first few examples
    const examples = auditReport.unknownEdges.slice(0, 3);
    console.log('\n  Examples:');
    for (const edge of examples) {
      console.log(
        `    - ${edge.type}: ${edge.source} → ${edge.target} (${edge.reason})`
      );
      if (edge.filePath) {
        console.log(
          `      📁 ${edge.filePath}${edge.lineNumber ? `:${edge.lineNumber}` : ''}`
        );
      }
    }

    if (auditReport.unknownEdges.length > 3) {
      console.log(`    ... and ${auditReport.unknownEdges.length - 3} more`);
    }
  } else {
    console.log('\n✅ No unknown edges detected');
  }

  // Performance
  console.log(`\n⚡ Performance:`);
  console.log(`  • Duration: ${auditReport.performance.durationMs}ms`);
  console.log(`  • Files Processed: ${auditReport.performance.filesProcessed}`);
  console.log(`  • Edges Analyzed: ${auditReport.performance.edgesAnalyzed}`);
  if (auditReport.performance.cacheHits) {
    console.log(`  • Cache Hits: ${auditReport.performance.cacheHits}`);
  }
}

/**
 * Determine exit code based on audit results
 * @param auditReport - Generated audit report
 * @param threshold - Coverage threshold
 * @returns Exit code (0=success, 61=coverage breach, 62=unknown edge cap)
 */
function determineExitCode(
  auditReport: AuditReport,
  threshold: number
): number {
  // Check coverage threshold
  const belowThreshold = auditReport.milestones.filter(
    (m: any) => m.coverage < threshold
  );
  if (belowThreshold.length > 0) {
    console.log(
      `\n❌ Coverage threshold breach: ${belowThreshold.length} milestones below ${(threshold * 100).toFixed(1)}%`
    );
    return 61; // Coverage breach (reuse from sync-kg)
  }

  // Check unknown edge cap (configurable, default 10)
  const unknownEdgeCap = 10;
  if (auditReport.unknownEdges.length > unknownEdgeCap) {
    console.log(
      `\n❌ Unknown edge cap exceeded: ${auditReport.unknownEdges.length} > ${unknownEdgeCap}`
    );
    return 62; // Unknown edge cap exceeded
  }

  return 0; // Success
}

/**
 * Save audit report to file
 * @param auditReport - Generated audit report
 * @param outputDir - Output directory
 * @param dryRun - Whether this is a dry run
 */
function saveAuditReport(
  auditReport: AuditReport,
  outputDir: string,
  dryRun: boolean
): void {
  if (dryRun) {
    console.log(
      `\n📋 Dry run - would save audit report to ${outputDir}/kg-audit.json`
    );
    return;
  }

  try {
    mkdirSync(outputDir, { recursive: true });
    const auditPath = join(outputDir, 'kg-audit.json');
    writeFileSync(auditPath, JSON.stringify(auditReport, null, 2), 'utf-8');
    console.log(`\n💾 Audit report saved: ${auditPath}`);
  } catch (error) {
    console.error(`❌ Failed to save audit report: ${error}`);
  }
}

/**
 * Main audit-kg command implementation
 * @param specsDirectory - Directory containing specifications
 * @param options - Command options
 */
async function auditKnowledgeGraph(
  specsDirectory: string,
  options: AuditCliOptions
): Promise<number> {
  const performanceStart = Date.now();
  const outputDir = options.outputDir || './output/kg';
  const threshold = options.failUnder || 0.5;

  try {
    console.log('🔍 Starting knowledge graph audit...');

    // Load existing knowledge graph
    const kgPath = join(outputDir, 'kg.jsonld');
    const { nodes, edges } = loadKnowledgeGraph(kgPath);

    if (nodes.length === 0 && edges.length === 0) {
      console.error(
        '❌ No knowledge graph data found. Run build-kg or sync-kg first.'
      );
      return 1;
    }

    console.log(`📊 Loaded ${nodes.length} nodes and ${edges.length} edges`);

    // Extract milestone components
    const milestoneComponents = extractMilestoneComponents(specsDirectory);
    console.log(`📋 Found ${milestoneComponents.size} milestones`);

    // Detect unknown edges
    console.log('🔍 Detecting unknown edges...');
    const unknownEdges = detectUnknownEdges(nodes, edges as any);

    // Group unknown edges by milestone
    const unknownEdgesByMilestone = groupUnknownEdgesByMilestone(unknownEdges);

    // Calculate coverage for all milestones
    console.log('📈 Calculating coverage metrics...');
    const implementsEdges = edges.filter((e) => e['@type'] === 'implements');
    const milestones = calculateAllMilestoneCoverage(
      implementsEdges,
      milestoneComponents,
      unknownEdgesByMilestone
    );

    // Calculate edge totals
    const edgeTotals = {
      implements: edges.filter((e) => e['@type'] === 'implements').length,
      workflow_calls: edges.filter((e: any) => e['@type'] === 'workflow_calls')
        .length,
      dependsOn: edges.filter((e) => e['@type'] === 'dependsOn').length,
      total: edges.length,
    };

    // Generate audit report
    const auditReport: AuditReport = {
      summary: {
        generatedAt: new Date().toISOString(),
        edgeTotals,
        gitRef: options.since || 'HEAD',
        filesScanned: nodes.length, // Approximate
      },
      milestones,
      unknownEdges,
      performance: {
        durationMs: Date.now() - performanceStart,
        filesProcessed: nodes.length,
        edgesAnalyzed: edges.length,
        cacheHits: 0,
      },
    };

    // Validate audit report
    console.log('✅ Validating audit report...');
    const validation = await validateAuditReport(auditReport);
    if (!validation.isValid) {
      console.warn('⚠️  Audit report validation warnings:');
      validation.errors.forEach((error) => console.warn(`  - ${error}`));
    }

    // Output results
    if (
      options.format === 'pretty' ||
      options.format === 'both' ||
      !options.format
    ) {
      printAuditResults(auditReport);
    }

    if (options.format === 'json' || options.format === 'both') {
      saveAuditReport(auditReport, outputDir, options.dryRun || false);
    }

    // Determine exit code
    const exitCode = determineExitCode(auditReport, threshold);

    if (exitCode === 0) {
      console.log('\n✅ Audit completed successfully!');
    }

    return exitCode;
  } catch (error: any) {
    console.error(`❌ Audit failed: ${error}`);
    return 1;
  }
}

// Set up CLI program
const program = new Command();

program
  .name('audit-kg')
  .description('Knowledge graph audit and confidence analysis')
  .version('2.0.0')
  .argument('<specs-directory>', 'Directory containing specification files')
  .option(
    '--since <commit-ish>',
    'Git reference for audit context (default: HEAD)'
  )
  .option(
    '--format <type>',
    'Output format: json, pretty, or both (default: pretty)',
    'pretty'
  )
  .option(
    '--fail-under <number>',
    'Coverage threshold for exit code (default: 0.5)',
    parseFloat
  )
  .option(
    '--output-dir <path>',
    'Output directory for audit files (default: ./output/kg)'
  )
  .option('--dry-run', 'Show what would be done without making changes')
  .option('--verbose', 'Enable verbose output')
  .action(async (specsDirectory: string, options: AuditCliOptions) => {
    const resolvedSpecsDir = resolve(specsDirectory);
    const exitCode = await auditKnowledgeGraph(resolvedSpecsDir, options);
    process.exit(exitCode);
  });

// Handle unhandled errors (reuse pattern from sync-kg)
process.on('unhandledRejection', (reason) => {
  console.error('❌ Unhandled rejection:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

// Parse command line arguments
program.parse();
