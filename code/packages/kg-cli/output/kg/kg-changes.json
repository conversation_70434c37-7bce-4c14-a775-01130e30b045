{"timestamp": "2025-06-04T05:58:16.568Z", "summary": {"nodesAdded": 13, "nodesUpdated": 2, "nodesMarkedStale": 0, "edgesAdded": 7, "edgesUpdated": 2, "edgesMarkedStale": 0}, "coverage": [], "errors": [{"message": "Multiple @implements annotations found for M2#AuditSchema", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "File name \"auditReport.ts\" doesn't match component \"AuditSchema\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "Duplicate @implements annotation for M2#AuditSchema", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "File name \"auditReport.ts\" doesn't match component \"AuditSchema\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/auditReport.ts", "line": 1}, {"message": "Multiple @implements annotations found for M2#Coverage", "severity": "warning", "file": "code/packages/kg-audit-lib/src/coverage.ts", "line": 1}, {"message": "Duplicate @implements annotation for M2#Coverage", "severity": "warning", "file": "code/packages/kg-audit-lib/src/coverage.ts", "line": 1}, {"message": "File name \"index.ts\" doesn't match component \"AuditLib\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/index.ts", "line": 1}, {"message": "File name \"types.ts\" doesn't match component \"AuditLib\"", "severity": "warning", "file": "code/packages/kg-audit-lib/src/types.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-cli/src/audit-kg.ts", "line": 1}, {"message": "File name \"audit-kg.ts\" doesn't match component \"AuditCli\"", "severity": "warning", "file": "code/packages/kg-cli/src/audit-kg.ts", "line": 1}, {"message": "File name \"updateGraph.ts\" doesn't match component \"GraphUpdateCore\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/updateGraph.ts", "line": 1}]}