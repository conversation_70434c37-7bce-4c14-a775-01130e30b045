"@context":
  "@vocab": https://workflow-mapper.dev/vocab#
  title: https://schema.org/name
  description: https://schema.org/description
  implements: https://workflow-mapper.dev/vocab#implements
  dependsOn: https://workflow-mapper.dev/vocab#dependsOn
"@graph":
  - "@id": spec--users-nitish-tmp-kloudi-swe-agent-code-packages-kg-cli-test-integration-specs-cli-test-mdx-cli-test
    "@type": Specification
    filePath: /Users/<USER>/tmp/kloudi-swe-agent/code/packages/kg-cli/test-integration/specs/cli-test.mdx
    title: CLI Test
    description: CLI integration test
    version: 1.0.0
    status: Draft
  - "@id": function:04ab5678
    "@type": function
    title: cli_test
    description: Function cli_test() in
      /Users/<USER>/tmp/kloudi-swe-agent/code/packages/kg-cli/test-integration/code/cli-test.py
    filePath: /Users/<USER>/tmp/kloudi-swe-agent/code/packages/kg-cli/test-integration/code/cli-test.py
    name: cli_test
    signature: cli_test()
    lang: python
    line_start: 2
    line_end: 3
  - "@id": function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport
    "@type": function
    filePath: code/packages/kg-audit-lib/src/auditReport.ts
    functionName: generateAuditReport
    lastVerified: 2025-06-04T05:51:59.282Z
  - "@id": component:M2#AuditSchema
    "@type": component
    milestoneId: M2
    componentName: AuditSchema
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage
    "@type": function
    filePath: code/packages/kg-audit-lib/src/coverage.ts
    functionName: calculateAuditCoverage
    lastVerified: 2025-06-04T05:51:59.284Z
  - "@id": component:M2#Coverage
    "@type": component
    milestoneId: M2
    componentName: Coverage
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary
    "@type": function
    filePath: code/packages/kg-audit-lib/src/index.ts
    functionName: initializeAuditLibrary
    lastVerified: 2025-06-04T05:51:59.285Z
  - "@id": component:M2#AuditLib
    "@type": component
    milestoneId: M2
    componentName: AuditLib
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes
    "@type": function
    filePath: code/packages/kg-audit-lib/src/types.ts
    functionName: validateAuditTypes
    lastVerified: 2025-06-04T05:51:59.286Z
  - "@id": function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges
    "@type": function
    filePath: code/packages/kg-audit-lib/src/unknownEdges.ts
    functionName: detectUnknownEdges
    lastVerified: 2025-06-04T05:51:59.287Z
  - "@id": component:M2#UnknownEdges
    "@type": component
    milestoneId: M2
    componentName: UnknownEdges
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": function:code/packages/kg-cli/src/audit-kg.ts#
    "@type": function
    filePath: code/packages/kg-cli/src/audit-kg.ts
    functionName: ""
    lastVerified: 2025-06-04T05:51:59.290Z
  - "@id": component:M2#AuditCli
    "@type": component
    milestoneId: M2
    componentName: AuditCli
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph
    "@type": function
    filePath: code/packages/kg-sync-lib/src/updateGraph.ts
    functionName: updateGraph
    lastVerified: 2025-06-04T05:51:59.291Z
  - "@id": component:M1.2#GraphUpdateCore
    "@type": component
    milestoneId: M1.2
    componentName: GraphUpdateCore
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": implements:function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport->component:M2#AuditSchema
    "@type": implements
    source: function:code/packages/kg-audit-lib/src/auditReport.ts#generateAuditReport
    target: component:M2#AuditSchema
    confidence: 1
    lastVerified: 2025-06-04T05:51:59.293Z
    stale: false
  - "@id": implements:function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage->component:M2#Coverage
    "@type": implements
    source: function:code/packages/kg-audit-lib/src/coverage.ts#calculateAuditCoverage
    target: component:M2#Coverage
    confidence: 1
    lastVerified: 2025-06-04T05:51:59.293Z
    stale: false
  - "@id": implements:function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary->component:M2#AuditLib
    "@type": implements
    source: function:code/packages/kg-audit-lib/src/index.ts#initializeAuditLibrary
    target: component:M2#AuditLib
    confidence: 1
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": implements:function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes->component:M2#AuditLib
    "@type": implements
    source: function:code/packages/kg-audit-lib/src/types.ts#validateAuditTypes
    target: component:M2#AuditLib
    confidence: 1
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": implements:function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges->component:M2#UnknownEdges
    "@type": implements
    source: function:code/packages/kg-audit-lib/src/unknownEdges.ts#detectUnknownEdges
    target: component:M2#UnknownEdges
    confidence: 1
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": implements:function:code/packages/kg-cli/src/audit-kg.ts#->component:M2#AuditCli
    "@type": implements
    source: function:code/packages/kg-cli/src/audit-kg.ts#
    target: component:M2#AuditCli
    confidence: 0.5
    lastVerified: 2025-06-04T05:51:59.293Z
  - "@id": implements:function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph->component:M1.2#GraphUpdateCore
    "@type": implements
    source: function:code/packages/kg-sync-lib/src/updateGraph.ts#updateGraph
    target: component:M1.2#GraphUpdateCore
    confidence: 1
    lastVerified: 2025-06-04T05:51:59.293Z
