# Agent Dogfooding Instructions

## **🎯 Concise Instructions for Agent Dogfooding**

### **Agent Dogfooding Task: Milestone Automation Scripts**

**Your Role**: You are a software agent tasked with executing milestone M1.2 using new automation scripts.

**Task**: Simulate using these scripts and report usability issues, confusion points, and improvements.

**Files to Review**:
- `milestone-guide.sh` - Main execution script
- `milestone-control.sh` - Human oversight interface
- `MILESTONE_AUTOMATION_README.md` - Documentation with flowchart

**Simulation Process**:
1. **Read the README** - Understand the workflow and flowchart
2. **Trace through milestone-guide.sh** - Follow the execution path as if running `./milestone-guide.sh M1.2 augment`
3. **Identify friction points** - What would confuse you? What might fail?
4. **Test human intervention** - How would you use milestone-control.sh for guidance?
5. **Check error scenarios** - What happens when things go wrong?

**Focus Areas**:
- **Instruction clarity** - Are the prompts and messages clear?
- **Error handling** - Do error messages help you recover?
- **Workflow logic** - Does the phase progression make sense?
- **Autonomous mode** - Would `--autonomous` flag work for you?
- **State management** - Can you understand where you are in the process?

**Report Format**:
```
## Agent Perspective: [Your Agent Type]

### What Works Well:
- [List clear, helpful aspects]

### Friction Points:
- [List confusing or problematic areas]

### Potential Failures:
- [List scenarios where script might fail]

### Suggestions:
- [List specific improvements]

### Overall Assessment:
- [Would you be comfortable using this system?]
```

**Time Estimate**: 30-45 minutes for thorough analysis

---

## **📋 Additional Context for Agents**

### **What This System Aims to Solve**:
- Agents skipping milestone reading (~80% of cases)
- Wrong branch usage (~60% of cases)
- Missing work log updates (~90% of cases)
- Inconsistent execution patterns
- Lack of human oversight during execution

### **Key Innovation**:
- **Phase 0**: Forces milestone analysis before execution
- **Async Messaging**: Human can send guidance anytime
- **State Persistence**: Always recoverable, never lose progress
- **Autonomous Mode**: Can run without human interaction
- **Recovery System**: Multiple options when things go wrong

### **Success Criteria**:
An agent should be able to:
1. Understand what to do at each step
2. Recover from common error scenarios
3. Communicate with human when stuck
4. Complete milestone execution reliably
5. Feel confident using the system

### **Testing Scenarios to Consider**:

#### **Happy Path**:
- Normal milestone execution from start to finish
- Human sends guidance during execution
- All phases transition correctly

#### **Error Scenarios**:
- Missing dependencies (jq, node, git)
- Malformed milestone file
- Git conflicts or branch issues
- Work log validation failures
- Acceptance test failures

#### **Edge Cases**:
- Very large milestones (>20 tasks)
- Milestones with unusual formatting
- Network issues during execution
- Interrupted execution (crash/restart)
- Multiple agents working simultaneously

### **Questions to Ask Yourself**:
1. Would I trust this system with a real milestone?
2. Are the error messages helpful enough to guide recovery?
3. Is the autonomous mode truly autonomous?
4. Would the human intervention system actually help?
5. Is the state management robust enough for production use?

---

## **📊 Dogfooding Results**

### **Agent Perspective: GitHub Copilot**

**What Works Well:**
- Clear Phase Structure: Both milestone-guide.sh and the README document a well-defined, multi-phase workflow (analysis, instruction generation, execution, finalization, recovery).
- Dependency Checks: milestone-guide.sh validates all required tools and scripts up front, with actionable error messages if missing.
- State Management: State is persistently stored in .milestone-state/{milestone-id}/, with clear JSON structure and phase/task tracking. Manual editing is supported and documented.
- Async Human-AI Messaging: Both scripts support a robust, persistent message system. Agents check for human messages at checkpoints; humans can send/answer at any time.
- Human Oversight: milestone-control.sh provides comprehensive controls: status, message, answer, pause, resume, reset, and help, all with clear usage instructions.
- Error Handling and Recovery: The guide script offers recovery options and clear error messages for missing files, failed validations, and more.
- Autonomous Mode: The --autonomous flag is respected throughout, allowing agents to proceed with defaults and minimal human input.
- Work Log Enforcement: The system blocks progression until work logs are updated, ensuring documentation is never skipped.
- Comprehensive Documentation: The README includes a flowchart, phase breakdown, file structure, state file examples, troubleshooting, and a detailed testing checklist.

**Friction Points:**
- Script Length and Complexity: Both shell scripts are long and have many branches, making it hard to trace all possible flows without careful reading.
- State File Corruption: If the state file is corrupted or manually edited incorrectly, recovery instructions are present but could be more prominent in the scripts themselves.
- Human Review Prompts: In non-autonomous mode, the script expects a numeric input (1 or 2) for review choices. If the user enters an invalid value, the script loops or defaults, but this could be more robust.
- Error Message Consistency: Some error messages are more actionable than others. For example, missing milestone files are handled well, but git errors or instruction generator failures could suggest more specific next steps.
- Manual State Editing: While supported, this is risky and could lead to subtle bugs if not done carefully. More guardrails or validation would help.
- No Phase 2: The phase numbering skips from 1 to 3, which could be confusing (README notes this, but scripts could clarify).
- Branch Management Details: The actual git operations for branch creation/cleanup are not shown in the reviewed lines, so it's unclear how robustly errors are handled there.

**Potential Failures:**
- Missing Dependencies: If jq, node, or git are missing, the script exits with a clear message, but does not attempt to auto-install or provide install commands.
- Malformed Milestone Files: If the milestone file is missing or malformed, the script exits and lists available files, but does not attempt to auto-fix or suggest how to repair.
- Spec-lint or Instruction Generator Fails: If these scripts fail, the guide script exits, but does not provide detailed debugging help.
- Git Conflicts: If a git operation fails (e.g., branch creation, merge), the error handling is not shown in the reviewed lines and may not be robust.
- Paused State: If the agent is paused, it will not proceed until resumed, but if the pause flag is not cleared correctly, the agent could get stuck.
- Multiple Agents: If two agents operate on the same milestone, state or message files could be overwritten or corrupted.

**Suggestions:**
- Add More Robust Input Validation: For all human prompts, especially numeric choices, add retries and clearer error messages.
- Improve Error Recovery Guidance: When a failure occurs, suggest specific commands or documentation sections to consult.
- Auto-Install Dependencies: Optionally offer to install missing dependencies or provide copy-paste install commands.
- Guard Manual State Editing: Add a validation script or command to check state file integrity after manual edits.
- Clarify Phase Numbering: Add comments or echo statements explaining the phase numbering (why Phase 2 is skipped).
- Expand Git Error Handling: Ensure all git operations are wrapped with error checks and actionable recovery steps.
- Concurrency Safeguards: Lock state files or warn if multiple agents are detected working on the same milestone.

**Overall Assessment:**
Yes, this system is robust, well-documented, and agent-friendly. The workflow is clear, state is persistent and recoverable, and human intervention is well-supported. Some minor improvements to error handling, input validation, and concurrency protection would make it even more reliable, but as-is, it is suitable for production use by agents and humans alike.

---

**Remember**: You're evaluating this from the perspective of an AI agent who needs to execute milestones reliably. Focus on practical usability rather than code perfection.
