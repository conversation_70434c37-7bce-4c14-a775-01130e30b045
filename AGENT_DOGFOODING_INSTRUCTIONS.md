# Agent Dogfooding Instructions

## **🎯 Concise Instructions for Agent Dogfooding**

### **Agent Dogfooding Task: Milestone Automation Scripts**

**Your Role**: You are a software agent tasked with executing milestone M1.2 using new automation scripts.

**Task**: Simulate using these scripts and report usability issues, confusion points, and improvements.

**Files to Review**:
- `milestone-guide.sh` - Main execution script
- `milestone-control.sh` - Human oversight interface  
- `MILESTONE_AUTOMATION_README.md` - Documentation with flowchart

**Simulation Process**:
1. **Read the README** - Understand the workflow and flowchart
2. **Trace through milestone-guide.sh** - Follow the execution path as if running `./milestone-guide.sh M1.2 augment`
3. **Identify friction points** - What would confuse you? What might fail?
4. **Test human intervention** - How would you use milestone-control.sh for guidance?
5. **Check error scenarios** - What happens when things go wrong?

**Focus Areas**:
- **Instruction clarity** - Are the prompts and messages clear?
- **Error handling** - Do error messages help you recover?
- **Workflow logic** - Does the phase progression make sense?
- **Autonomous mode** - Would `--autonomous` flag work for you?
- **State management** - Can you understand where you are in the process?

**Report Format**:
```
## Agent Perspective: [Your Agent Type]

### What Works Well:
- [List clear, helpful aspects]

### Friction Points:
- [List confusing or problematic areas]

### Potential Failures:
- [List scenarios where script might fail]

### Suggestions:
- [List specific improvements]

### Overall Assessment:
- [Would you be comfortable using this system?]
```

**Time Estimate**: 30-45 minutes for thorough analysis

---

## **📋 Additional Context for Agents**

### **What This System Aims to Solve**:
- Agents skipping milestone reading (~80% of cases)
- Wrong branch usage (~60% of cases)
- Missing work log updates (~90% of cases)
- Inconsistent execution patterns
- Lack of human oversight during execution

### **Key Innovation**:
- **Phase 0**: Forces milestone analysis before execution
- **Async Messaging**: Human can send guidance anytime
- **State Persistence**: Always recoverable, never lose progress
- **Autonomous Mode**: Can run without human interaction
- **Recovery System**: Multiple options when things go wrong

### **Success Criteria**:
An agent should be able to:
1. Understand what to do at each step
2. Recover from common error scenarios
3. Communicate with human when stuck
4. Complete milestone execution reliably
5. Feel confident using the system

### **Testing Scenarios to Consider**:

#### **Happy Path**:
- Normal milestone execution from start to finish
- Human sends guidance during execution
- All phases transition correctly

#### **Error Scenarios**:
- Missing dependencies (jq, node, git)
- Malformed milestone file
- Git conflicts or branch issues
- Work log validation failures
- Acceptance test failures

#### **Edge Cases**:
- Very large milestones (>20 tasks)
- Milestones with unusual formatting
- Network issues during execution
- Interrupted execution (crash/restart)
- Multiple agents working simultaneously

### **Questions to Ask Yourself**:
1. Would I trust this system with a real milestone?
2. Are the error messages helpful enough to guide recovery?
3. Is the autonomous mode truly autonomous?
4. Would the human intervention system actually help?
5. Is the state management robust enough for production use?

---

**Remember**: You're evaluating this from the perspective of an AI agent who needs to execute milestones reliably. Focus on practical usability rather than code perfection.
