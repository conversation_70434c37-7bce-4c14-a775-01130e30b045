#!/bin/bash

# Milestone Control Script
# Human interface for monitoring and controlling milestone execution
# Usage: ./milestone-control.sh <milestone-id> <command> [args...]

# ═══════════════════════════════════════════════════════════════════════════════
# 📋 SCRIPT FLOW CHART - How to Read This Script Line by Line
# ═══════════════════════════════════════════════════════════════════════════════
#
# ENTRY POINT:
# main() [line ~150] → Parse arguments → Route to command
#
# COMMAND ROUTING (based on $COMMAND):
# main() → case "$COMMAND" in:
#   ├── "status"      → show_status() [line ~300]
#   ├── "message"     → send_message() [line ~420]
#   ├── "answer"      → send_answer() [line ~470]
#   ├── "messages"    → show_messages() [line ~510]
#   ├── "pause"       → pause_execution() [line ~530]
#   ├── "resume"      → resume_execution() [line ~550]
#   ├── "reset"       → reset_milestone() [line ~570]
#   ├── "help"        → show_contextual_help() [line ~260]
#   ├── "help-basic"  → show_help() [line ~200]
#   └── *             → show_usage() [line ~180] → exit
#
# STATUS COMMAND WORKFLOW (MAIN MONITORING):
# show_status() [line ~300]:
#   ├── Check if state file exists
#   │   └── No → Show "milestone not started" message
#   ├── Extract state information (phase, task, agent, timestamps)
#   ├── analyze_current_situation() [line ~50] → Detect stuck/conflict patterns
#   ├── calculate_task_duration() [line ~20] → Human-readable time format
#   ├── show_situational_awareness() [line ~90] → Context-specific alerts
#   │   ├── "stuck_long" → 🚨 URGENT alerts + recovery suggestions
#   │   ├── "stuck_medium" → ⚠️ warnings + guidance options
#   │   ├── "git_conflict" → 🔧 git-specific help + resolution steps
#   │   ├── "analysis_slow" → 🔍 milestone analysis guidance
#   │   └── "normal" → ✅ normal status + available actions
#   ├── Show git status (branch, modified files)
#   ├── Show recent activity (last 3 phase changes)
#   ├── check_message_status() [line ~390] → Show unread message counts
#   └── Show available control options
#
# CONTEXTUAL HELP WORKFLOW (INTELLIGENT GUIDANCE):
# show_contextual_help() [line ~260]:
#   ├── Check if state file exists
#   ├── analyze_current_situation() [line ~50] → Detect current context
#   ├── Provide situation-specific guidance:
#   │   ├── "stuck_long"/"stuck_medium" → 🚨 Stuck agent recovery steps
#   │   │   ├── 1. Send check-in message
#   │   │   ├── 2. Review message history
#   │   │   ├── 3. Reset to previous task
#   │   │   └── 4. Pause for investigation
#   │   ├── "git_conflict" → 🔧 Git conflict resolution steps
#   │   │   ├── 1. Offer help message
#   │   │   ├── 2. Check git status details
#   │   │   ├── 3. Guide resolution commands
#   │   │   └── 4. Reset option
#   │   ├── "analysis_slow" → 🔍 Milestone analysis troubleshooting
#   │   │   ├── 1. Check milestone file
#   │   │   ├── 2. Ask agent about issues
#   │   │   └── 3. Check dependencies
#   │   └── "normal" → ✅ Phase-specific normal operations
#   │       ├── task_execution → Monitor, guide, check progress
#   │       ├── pre_review → Monitor, provide input
#   │       └── other → General actions
#   └── Show other available commands
#
# MESSAGE COMMAND WORKFLOW (HUMAN-AGENT COMMUNICATION):
# send_message() [line ~420]:
#   ├── Validate message text provided
#   ├── enhance_message() [line ~220] → Add context and urgency
#   │   ├── Add context for vague messages (task/phase info)
#   │   ├── Add urgency indicators based on situation:
#   │   │   ├── "stuck_long" → 🚨 URGENT prefix
#   │   │   └── "stuck_medium"/"git_conflict" → ⚠️ warning prefix
#   │   └── Provide tips for common vague messages:
#   │       ├── "fix it" → Suggest being more specific
#   │       ├── "help" → Suggest specifying help type
#   │       └── "stuck" → Suggest describing what stuck on
#   ├── Create message JSON entry with timestamp
#   ├── Add to persistent messages file
#   └── Confirm message queued for agent
#
# ANSWER COMMAND WORKFLOW (RESPONDING TO AGENT QUESTIONS):
# send_answer() [line ~470]:
#   ├── Validate answer text provided
#   ├── Mark previous agent questions as "answered"
#   ├── Create answer message JSON entry
#   ├── Add to persistent messages file
#   └── Confirm answer sent to agent
#
# CONTROL COMMANDS WORKFLOW:
# pause_execution() [line ~530]:
#   ├── Check state file exists
#   ├── Add pause flag to state file
#   ├── Set pause reason and timestamp
#   └── Show resume instructions
#
# resume_execution() [line ~550]:
#   ├── Check state file exists
#   ├── Remove pause flags from state file
#   ├── Update timestamp
#   └── Confirm execution can continue
#
# reset_milestone() [line ~570]:
#   ├── Check state file exists
#   ├── If task number provided:
#   │   └── Reset to specific task in task_execution phase
#   ├── If no task number:
#   │   └── Reset to milestone start (not_started phase)
#   ├── Update state file with new phase/task
#   └── Show instructions to continue
#
# HELPER FUNCTIONS (called throughout):
# ├── Situational Analysis:
# │   ├── calculate_task_duration() [line ~20] - Convert timestamps to human format
# │   ├── analyze_current_situation() [line ~50] - Detect stuck/conflict patterns
# │   ├── detect_git_issues() [line ~80] - Check for merge conflicts
# │   └── show_situational_awareness() [line ~90] - Context-specific alerts
# ├── Message Enhancement:
# │   └── enhance_message() [line ~220] - Add context and urgency to messages
# ├── Message Status:
# │   └── check_message_status() [line ~390] - Show unread message counts
# └── Display Functions:
#     ├── show_usage() [line ~180] - Basic usage information
#     └── show_help() [line ~200] - Detailed help documentation
#
# SITUATIONAL AWARENESS PATTERNS:
# analyze_current_situation() detects:
# ├── "stuck_long" → No progress for 45+ minutes
# ├── "stuck_medium" → No progress for 20+ minutes in task_execution
# ├── "git_conflict" → Merge conflicts or working on main branch
# ├── "analysis_slow" → pre_review phase taking 10+ minutes
# └── "normal" → Everything proceeding as expected
#
# MESSAGE ENHANCEMENT PATTERNS:
# enhance_message() improves:
# ├── Vague messages → Add current task/phase context
# ├── Urgent situations → Add 🚨 URGENT or ⚠️ warning prefixes
# ├── Common patterns → Provide specific improvement tips
# └── Context-free messages → Add situational information
#
# FILE STRUCTURE INTERACTION:
# ├── State File: .milestone-state/{milestone-id}/current-state.json
# │   ├── Read: current_phase, current_task, total_tasks, timestamps
# │   └── Write: pause flags, reset phase/task, timestamps
# └── Messages File: .milestone-state/{milestone-id}/human-messages.json
#     ├── Read: unread message counts, message history
#     └── Write: new messages, answer responses, status updates
#
# ERROR HANDLING:
# ├── Missing state file → Show "milestone not started" guidance
# ├── Missing message text → Show usage and exit
# ├── Invalid JSON → Graceful error messages
# └── Missing milestone directory → Auto-create when needed
#
# ═══════════════════════════════════════════════════════════════════════════════

set -euo pipefail

# Global variables
MILESTONE_ID=""
STATE_DIR=""
STATE_FILE=""
MESSAGES_FILE=""

# Situational awareness functions
calculate_task_duration() {
    local last_updated="$1"

    if [[ "$last_updated" == "unknown" ]]; then
        echo "unknown"
        return
    fi

    # Calculate duration since last update
    local last_epoch=$(date -d "$last_updated" +%s 2>/dev/null || echo "0")
    local current_epoch=$(date +%s)
    local duration_seconds=$((current_epoch - last_epoch))
    local duration_minutes=$((duration_seconds / 60))

    if [[ $duration_minutes -lt 1 ]]; then
        echo "just now"
    elif [[ $duration_minutes -lt 60 ]]; then
        echo "${duration_minutes}m"
    else
        local duration_hours=$((duration_minutes / 60))
        local remaining_minutes=$((duration_minutes % 60))
        echo "${duration_hours}h ${remaining_minutes}m"
    fi
}

analyze_current_situation() {
    local phase="$1"
    local last_updated="$2"
    local current_task="$3"

    local duration_minutes=0
    if [[ "$last_updated" != "unknown" ]]; then
        local last_epoch=$(date -d "$last_updated" +%s 2>/dev/null || echo "0")
        local current_epoch=$(date +%s)
        duration_minutes=$(( (current_epoch - last_epoch) / 60 ))
    fi

    # Detect stuck patterns
    if [[ $duration_minutes -gt 45 ]]; then
        echo "stuck_long"
    elif [[ $duration_minutes -gt 20 ]] && [[ "$phase" == "task_execution" ]]; then
        echo "stuck_medium"
    elif [[ "$phase" == "task_execution" ]] && detect_git_issues; then
        echo "git_conflict"
    elif [[ "$phase" == "pre_review" ]] && [[ $duration_minutes -gt 10 ]]; then
        echo "analysis_slow"
    else
        echo "normal"
    fi
}

detect_git_issues() {
    if command -v git >/dev/null && [[ -d ".git" ]]; then
        # Check for merge conflicts
        if git status --porcelain 2>/dev/null | grep -q "^UU\|^AA\|^DD"; then
            return 0  # Git conflict detected
        fi

        # Check for uncommitted changes on main
        if [[ "$(git branch --show-current 2>/dev/null)" == "main" ]] && ! git diff --quiet 2>/dev/null; then
            return 0  # Working on main branch
        fi
    fi

    return 1  # No git issues
}

show_situational_awareness() {
    local situation="$1"
    local duration="$2"
    local phase="$3"
    local task="$4"

    echo ""
    case "$situation" in
        "stuck_long")
            echo "🚨 ATTENTION: Agent appears stuck (no progress for $duration)"
            echo "💡 Last activity was over 45 minutes ago"
            echo "🎯 Recommended actions:"
            echo "  1. Check what's happening: $0 $MILESTONE_ID messages"
            echo "  2. Send guidance: $0 $MILESTONE_ID message 'Status update please'"
            echo "  3. Consider reset: $0 $MILESTONE_ID reset $((task-1))"
            ;;
        "stuck_medium")
            echo "⚠️ Agent may be stuck (no progress for $duration)"
            echo "💡 Task $task has been running longer than usual"
            echo "🎯 Consider:"
            echo "  1. Send guidance: $0 $MILESTONE_ID message 'How is task $task going?'"
            echo "  2. Check for issues: Look for error patterns"
            ;;
        "git_conflict")
            echo "🔧 Git conflict detected"
            echo "💡 Agent may need help resolving merge conflicts"
            echo "🎯 Try:"
            echo "  1. Guide resolution: $0 $MILESTONE_ID message 'Need help with git conflict?'"
            echo "  2. Check git status: git status"
            echo "  3. Reset if needed: $0 $MILESTONE_ID reset $((task-1))"
            ;;
        "analysis_slow")
            echo "🔍 Milestone analysis taking longer than usual ($duration)"
            echo "💡 Agent may be encountering complex milestone or validation issues"
            echo "🎯 Consider:"
            echo "  1. Check milestone file for issues"
            echo "  2. Send guidance: $0 $MILESTONE_ID message 'Any issues with milestone analysis?'"
            ;;
        "normal")
            echo "✅ Execution proceeding normally"
            if [[ "$phase" == "task_execution" ]]; then
                echo "💡 Agent is actively working on task $task"
            fi
            ;;
    esac
}

# Main function
main() {
    if [[ $# -lt 2 ]]; then
        show_usage
        exit 1
    fi

    MILESTONE_ID="$1"
    COMMAND="$2"
    shift 2

    # Setup paths
    STATE_DIR=".milestone-state/$MILESTONE_ID"
    STATE_FILE="$STATE_DIR/current-state.json"
    MESSAGES_FILE="$STATE_DIR/human-messages.json"

    # Route to command
    case "$COMMAND" in
        "status")    show_status ;;
        "message")   send_message "$@" ;;
        "answer")    send_answer "$@" ;;
        "messages")  show_messages ;;
        "pause")     pause_execution ;;
        "resume")    resume_execution ;;
        "reset")     reset_milestone "$@" ;;
        "help")      show_contextual_help ;;
        "help-basic") show_help ;;
        *)           echo "Unknown command: $COMMAND"; show_usage; exit 1 ;;
    esac
}

show_usage() {
    cat << EOF
Usage: $0 <milestone-id> <command> [args...]

Commands:
  status                    Show current milestone status
  message <text>           Send message to agent
  answer <text>            Answer agent's question
  messages                 Show message history
  pause                    Pause agent execution
  resume                   Resume agent execution
  reset [task-number]      Reset to specific task or milestone start
  help                     Show detailed help

Examples:
  $0 M1.2 status
  $0 M1.2 message "Focus on error handling, skip optimization"
  $0 M1.2 answer "Use the regex approach"
  $0 M1.2 reset 3
EOF
}

show_help() {
    cat << EOF
Milestone Control - Detailed Help
================================

This script provides human oversight and control over milestone execution.

MONITORING:
  status     - Shows current phase, task progress, git state, recent activity
  messages   - Shows full conversation history between human and agent

COMMUNICATION:
  message    - Send guidance to agent (checked at natural checkpoints)
  answer     - Respond to agent questions

CONTROL:
  pause      - Stops agent execution (sets pause flag in state)
  resume     - Resumes agent execution (removes pause flag)
  reset      - Resets execution to specific point

STATE MANAGEMENT:
  State files are stored in: .milestone-state/$MILESTONE_ID/
  - current-state.json: Execution state and progress
  - human-messages.json: Conversation history

You can also manually edit state files for fine-grained control.

ASYNC INTERVENTION:
  Messages are persistent and checked by agent at natural checkpoints.
  Agent will acknowledge messages before continuing execution.
EOF
}

enhance_message() {
    local message="$1"
    local enhanced="$message"

    # Add context if state file exists
    if [[ -f "$STATE_FILE" ]]; then
        local current_phase=$(jq -r '.current_phase // "unknown"' "$STATE_FILE")
        local current_task=$(jq -r '.current_task // 0' "$STATE_FILE")
        local total_tasks=$(jq -r '.total_tasks // 0' "$STATE_FILE")

        # Add context for vague messages
        if [[ "$message" =~ (fix|help|issue|problem|stuck) ]] && [[ ! "$message" =~ (task|phase|file|error) ]]; then
            enhanced="$message (Context: Currently on task $current_task of $total_tasks, phase: $current_phase)"
        fi

        # Add urgency indicators
        local situation=$(analyze_current_situation "$current_phase" "$(jq -r '.last_updated // "unknown"' "$STATE_FILE")" "$current_task")
        case "$situation" in
            "stuck_long")
                enhanced="🚨 URGENT: $enhanced"
                ;;
            "stuck_medium"|"git_conflict")
                enhanced="⚠️ $enhanced"
                ;;
        esac
    fi

    # Suggest improvements for common vague messages
    case "$message" in
        "fix it"|"fix this"|"fix the issue")
            echo "💡 Tip: Consider being more specific about what to fix" >&2
            ;;
        "help"|"need help")
            echo "💡 Tip: Specify what kind of help you need" >&2
            ;;
        "stuck")
            echo "💡 Tip: Describe what you're stuck on" >&2
            ;;
    esac

    echo "$enhanced"
}

show_contextual_help() {
    echo "🆘 Contextual Help for $MILESTONE_ID"
    echo "===================================="

    if [[ ! -f "$STATE_FILE" ]]; then
        echo "❌ No state file found. Start with:"
        echo "  ./milestone-guide.sh $MILESTONE_ID"
        return
    fi

    local current_phase=$(jq -r '.current_phase // "unknown"' "$STATE_FILE")
    local current_task=$(jq -r '.current_task // 0' "$STATE_FILE")
    local last_updated=$(jq -r '.last_updated // "unknown"' "$STATE_FILE")
    local situation=$(analyze_current_situation "$current_phase" "$last_updated" "$current_task")

    echo "Current situation: $situation in $current_phase"
    echo ""

    case "$situation" in
        "stuck_long"|"stuck_medium")
            echo "🚨 Agent Appears Stuck - Try These:"
            echo "  1. 📞 Check in: $0 $MILESTONE_ID message 'Status update please - how is task $current_task going?'"
            echo "  2. 🔍 Review messages: $0 $MILESTONE_ID messages"
            echo "  3. 🔄 Reset if needed: $0 $MILESTONE_ID reset $((current_task-1))"
            echo "  4. ⏸️ Pause to investigate: $0 $MILESTONE_ID pause"
            ;;
        "git_conflict")
            echo "🔧 Git Conflict Detected - Try These:"
            echo "  1. 🆘 Offer help: $0 $MILESTONE_ID message 'I see a git conflict. Need help resolving it?'"
            echo "  2. 🔍 Check details: git status"
            echo "  3. 📋 Guide resolution: $0 $MILESTONE_ID message 'Try: git add . && git commit -m \"resolve conflict\"'"
            echo "  4. 🔄 Reset option: $0 $MILESTONE_ID reset $((current_task-1))"
            ;;
        "analysis_slow")
            echo "🔍 Slow Milestone Analysis - Try These:"
            echo "  1. 📋 Check milestone file for issues"
            echo "  2. 💬 Ask agent: $0 $MILESTONE_ID message 'Any issues with milestone analysis?'"
            echo "  3. 🔧 Check dependencies: Ensure spec-lint and other tools are available"
            ;;
        "normal")
            case "$current_phase" in
                "task_execution")
                    echo "✅ Normal Task Execution - Available Actions:"
                    echo "  1. 📊 Monitor: $0 $MILESTONE_ID status"
                    echo "  2. 💬 Send guidance: $0 $MILESTONE_ID message 'Focus on error handling'"
                    echo "  3. 📝 Check progress: $0 $MILESTONE_ID messages"
                    ;;
                "pre_review")
                    echo "✅ Normal Analysis Phase - Available Actions:"
                    echo "  1. 📊 Monitor: $0 $MILESTONE_ID status"
                    echo "  2. 💬 Provide input: $0 $MILESTONE_ID message 'Focus on security aspects'"
                    ;;
                *)
                    echo "✅ Normal Execution - General Actions:"
                    echo "  1. 📊 Check status: $0 $MILESTONE_ID status"
                    echo "  2. 💬 Send message: $0 $MILESTONE_ID message 'your guidance'"
                    echo "  3. 📝 View history: $0 $MILESTONE_ID messages"
                    ;;
            esac
            ;;
    esac

    echo ""
    echo "📚 Other Available Commands:"
    echo "  • $0 $MILESTONE_ID pause     - Pause execution"
    echo "  • $0 $MILESTONE_ID resume    - Resume execution"
    echo "  • $0 $MILESTONE_ID reset N   - Reset to task N"
    echo "  • $0 $MILESTONE_ID help-basic - Show basic help"
}

show_status() {
    echo "📊 Milestone $MILESTONE_ID Status"
    echo "================================"

    if [[ ! -f "$STATE_FILE" ]]; then
        echo "❌ No state file found. Milestone not started or state corrupted."
        echo "💡 Try running: ./milestone-guide.sh $MILESTONE_ID"
        return
    fi

    # Extract state information
    local current_phase=$(jq -r '.current_phase // "unknown"' "$STATE_FILE")
    local current_task=$(jq -r '.current_task // 0' "$STATE_FILE")
    local total_tasks=$(jq -r '.total_tasks // 0' "$STATE_FILE")
    local agent_type=$(jq -r '.agent_type // "unknown"' "$STATE_FILE")
    local started_at=$(jq -r '.started_at // "unknown"' "$STATE_FILE")
    local last_updated=$(jq -r '.last_updated // "unknown"' "$STATE_FILE")

    # Analyze current situation
    local situation=$(analyze_current_situation "$current_phase" "$last_updated" "$current_task")
    local task_duration=$(calculate_task_duration "$last_updated")

    echo "Agent: $agent_type"
    echo "Started: $started_at"
    echo "Last Updated: $last_updated ($task_duration ago)"
    echo ""

    # Show current progress with context
    echo "📋 Current Progress:"
    echo "  Phase: $current_phase"
    echo "  Task: $current_task of $total_tasks"

    if [[ $total_tasks -gt 0 ]]; then
        local progress_percent=$(( (current_task - 1) * 100 / total_tasks ))
        echo "  Progress: $progress_percent%"
    fi

    # Show situational awareness
    show_situational_awareness "$situation" "$task_duration" "$current_phase" "$current_task"

    # Show git status if available
    if command -v git >/dev/null && [[ -d ".git" ]]; then
        echo ""
        echo "🔧 Git Status:"
        echo "  Branch: $(git branch --show-current 2>/dev/null || echo 'unknown')"
        echo "  Modified files: $(git status --porcelain 2>/dev/null | wc -l || echo '0')"
    fi

    # Show recent activity
    echo ""
    echo "📈 Recent Activity:"
    jq -r '.phase_history[-3:] | .[] | "  [\(.timestamp)] \(.phase) (task \(.task // "N/A"))"' "$STATE_FILE" 2>/dev/null || echo "  No recent activity"

    # Check for unread messages
    check_message_status

    # Show available controls
    echo ""
    echo "🎛️ Available Controls:"
    echo "  [m] Send message    [p] Pause    [r] Reset    [h] Help"
    echo "  Example: $0 $MILESTONE_ID message \"your guidance here\""
}

check_message_status() {
    if [[ ! -f "$MESSAGES_FILE" ]]; then
        return
    fi

    local unread_to_agent=$(jq '[.messages[] | select(.to == "agent" and .status == "unread")] | length' "$MESSAGES_FILE" 2>/dev/null || echo "0")
    local unread_to_human=$(jq '[.messages[] | select(.to == "human" and .status == "unread")] | length' "$MESSAGES_FILE" 2>/dev/null || echo "0")

    echo ""
    echo "💬 Messages:"
    echo "  Unread messages to agent: $unread_to_agent"
    echo "  Unread messages to you: $unread_to_human"

    if [[ $unread_to_human -gt 0 ]]; then
        echo ""
        echo "❓ Agent has questions for you:"
        jq -r '.messages[] | select(.to == "human" and .status == "unread") | "  [\(.timestamp)] \(.message)"' "$MESSAGES_FILE" 2>/dev/null
        echo ""
        echo "💡 Answer with: $0 $MILESTONE_ID answer \"your response\""
    fi
}

send_message() {
    if [[ $# -eq 0 ]]; then
        echo "❌ Message text required"
        echo "Usage: $0 $MILESTONE_ID message \"your message here\""
        exit 1
    fi

    local raw_message="$*"
    local enhanced_message=$(enhance_message "$raw_message")

    echo "📤 Sending enhanced message to agent..."
    echo "Original: $raw_message"
    if [[ "$enhanced_message" != "$raw_message" ]]; then
        echo "Enhanced: $enhanced_message"
    fi

    # Create message entry
    local new_message=$(jq -n \
        --arg id "$(date +%s)" \
        --arg timestamp "$(date -Iseconds)" \
        --arg message "$enhanced_message" \
        '{
            id: $id,
            timestamp: $timestamp,
            from: "human",
            to: "agent",
            message: $message,
            status: "unread"
        }')

    # Add to messages file
    mkdir -p "$STATE_DIR"

    if [[ -f "$MESSAGES_FILE" ]]; then
        jq ".messages += [$new_message]" "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
    else
        echo "{\"messages\": [$new_message]}" > "$MESSAGES_FILE"
    fi

    echo "✅ Message sent and queued for agent"
    echo "💡 Agent will see this at the next checkpoint"
}

send_answer() {
    if [[ $# -eq 0 ]]; then
        echo "❌ Answer text required"
        echo "Usage: $0 $MILESTONE_ID answer \"your answer here\""
        exit 1
    fi

    local answer="$*"

    echo "📤 Sending answer to agent..."
    echo "Answer: $answer"

    # Mark agent questions as answered and add response
    if [[ -f "$MESSAGES_FILE" ]]; then
        # Mark previous agent questions as answered
        jq '(.messages[] | select(.to == "human" and .status == "unread") | .status) = "answered"' \
            "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
    fi

    # Add answer message
    local answer_message=$(jq -n \
        --arg id "$(date +%s)" \
        --arg timestamp "$(date -Iseconds)" \
        --arg message "$answer" \
        '{
            id: $id,
            timestamp: $timestamp,
            from: "human",
            to: "agent",
            message: $message,
            status: "unread"
        }')

    mkdir -p "$STATE_DIR"

    if [[ -f "$MESSAGES_FILE" ]]; then
        jq ".messages += [$answer_message]" "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
    else
        echo "{\"messages\": [$answer_message]}" > "$MESSAGES_FILE"
    fi

    echo "✅ Answer sent to agent"
}

show_messages() {
    echo "📬 Message History for $MILESTONE_ID"
    echo "===================================="

    if [[ ! -f "$MESSAGES_FILE" ]]; then
        echo "No messages found."
        return
    fi

    echo ""
    jq -r '.messages[] | "[\(.timestamp)] \(.from) → \(.to): \(.message) (\(.status))"' "$MESSAGES_FILE" 2>/dev/null || echo "Error reading messages"
}

pause_execution() {
    echo "⏸️ Pausing milestone execution..."

    if [[ ! -f "$STATE_FILE" ]]; then
        echo "❌ No state file found"
        exit 1
    fi

    # Add pause flag to state
    jq '.paused = true | .pause_reason = "Human requested pause" | .paused_at = now | .last_updated = (now | todate)' \
        "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

    echo "✅ Execution paused"
    echo "💡 Agent will see pause flag at next checkpoint"
    echo "💡 Resume with: $0 $MILESTONE_ID resume"
}

resume_execution() {
    echo "▶️ Resuming milestone execution..."

    if [[ ! -f "$STATE_FILE" ]]; then
        echo "❌ No state file found"
        exit 1
    fi

    # Remove pause flag
    jq 'del(.paused) | del(.pause_reason) | del(.paused_at) | .last_updated = (now | todate)' \
        "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

    echo "✅ Execution resumed"
    echo "💡 Agent can continue when next run"
}

reset_milestone() {
    local reset_task="$1"

    if [[ ! -f "$STATE_FILE" ]]; then
        echo "❌ No state file found"
        exit 1
    fi

    echo "🔄 Resetting milestone execution..."

    if [[ -n "$reset_task" ]]; then
        echo "Resetting to task: $reset_task"
        jq --arg task "$reset_task" \
            '.current_phase = "task_execution" | .current_task = ($task | tonumber) | .last_updated = (now | todate)' \
            "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"
    else
        echo "Resetting to milestone start"
        jq '.current_phase = "not_started" | .current_task = 1 | .last_updated = (now | todate)' \
            "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"
    fi

    echo "✅ Reset complete"
    echo "💡 Run milestone guide to continue: ./milestone-guide.sh $MILESTONE_ID"
}

# Run main function
main "$@"
