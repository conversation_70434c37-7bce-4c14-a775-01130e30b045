# Knowledge Graph Audit Workflow
# @implements milestone-M2#CiWorkflow
name: KG Audit

on:
  pull_request:
    branches: [main]
    paths:
      - 'code/**'
      - 'docs/tech-specs/**'
      - '.github/workflows/kg-audit.yml'
  schedule:
    - cron: '0 3 * * *'   # nightly UTC
  workflow_dispatch:

jobs:
  audit:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for git diff analysis
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.11.0'
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.4
          
      - name: Enable corepack
        run: corepack enable
        
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Build packages
        run: pnpm build
        
      - name: Generate knowledge graph
        run: |
          cd code
          pnpm run sync-kg ../docs/tech-specs
        
      - name: Run knowledge graph audit
        run: |
          cd code
          pnpm run audit-kg ../docs/tech-specs --format both --fail-under 0.5
        
      - name: Upload audit report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: kg-audit-report
          path: |
            code/output/kg/kg-audit.json
            code/output/kg/kg.jsonld
          retention-days: 30
          
      - name: Comment PR with audit results
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            try {
              const auditPath = path.join('code', 'output', 'kg', 'kg-audit.json');
              if (fs.existsSync(auditPath)) {
                const auditData = JSON.parse(fs.readFileSync(auditPath, 'utf8'));
                
                const milestones = auditData.milestones || [];
                const unknownEdges = auditData.unknownEdges || [];
                const performance = auditData.performance || {};
                
                const coverageResults = milestones
                  .filter(m => m.coverage > 0)
                  .map(m => `- **${m.id}**: ${(m.coverage * 100).toFixed(1)}% coverage, ${(m.confidence * 100).toFixed(1)}% confidence`)
                  .join('\n');
                
                const comment = `## 🔍 Knowledge Graph Audit Results
                
**Performance**: ${performance.durationMs || 0}ms execution time
**Unknown Edges**: ${unknownEdges.length} detected
                
### Coverage by Milestone
${coverageResults || 'No milestones with coverage found'}
                
### Unknown Edges Summary
${unknownEdges.length > 0 ? 
  `Found ${unknownEdges.length} unknown edges that may need attention.` : 
  'No unknown edges detected - excellent! 🎉'}
                
<details>
<summary>View detailed audit report</summary>

\`\`\`json
${JSON.stringify(auditData, null, 2)}
\`\`\`

</details>`;
                
                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            } catch (error) {
              console.log('Could not read audit report:', error.message);
            }
