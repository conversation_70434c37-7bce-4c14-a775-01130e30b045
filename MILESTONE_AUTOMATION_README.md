# Milestone Automation Scripts

Automated milestone execution with human-AI collaboration.

## 🚀 Quick Start

### For Agents (Primary Interface)
```bash
# Execute a milestone
./milestone-guide.sh M1.2 augment
```

### For Humans (Oversight Interface)
```bash
# Check status
./milestone-control.sh M1.2 status

# Send guidance
./milestone-control.sh M1.2 message "Focus on error handling, skip optimization"

# Answer agent questions
./milestone-control.sh M1.2 answer "Use the regex approach"
```

## 📋 How It Works

### Complete Workflow Flowchart

```
📋 MILESTONE EXECUTION WORKFLOW
═══════════════════════════════════════════════════════════════════

START: Human initiates
┌─────────────────────┐
│ Human: "Execute     │ 👨‍💼 HUMAN TRIGGER
│ milestone M1.2"     │
└─────────┬───────────┘
          │
          ▼
┌─────────────────────┐
│ ./milestone-guide.sh│ 🤖 AGENT ENTRY POINT
│ M1.2 augment        │
└─────────┬───────────┘
          │
          ▼
╔═════════════════════╗
║ PHASE 0A:           ║ 🤖 AGENT QUICK ANALYSIS
║ QUICK ANALYSIS      ║
║                     ║
║ • Extract milestone ║
║   info              ║
║ • Count tasks/      ║
║   criteria          ║
║ • Identify obvious  ║
║   red flags         ║
╚═════════┬═══════════╝
          │
          ▼
┌─────────────────────┐
│ Quick analysis      │ 🤖 AUTOMATIC PROGRESSION
│ complete            │ "Found X concerns, doing detailed analysis..."
│ → Auto continue     │
└─────────┬───────────┘
          │
          ▼
╔═════════════════════╗
║ PHASE 0B:           ║ 🤖 AGENT DETAILED ANALYSIS
║ DETAILED ANALYSIS   ║
║                     ║
║ • Task-by-task      ║
║   breakdown         ║
║ • Risk assessment   ║
║ • Specific          ║
║   recommendations   ║
║ • spec-lint         ║
║   validation        ║
╚═════════┬═══════════╝
          │
          ▼
┌─────────────────────┐
│ Agent presents      │ 🤖 → 👨‍💼 HANDOFF
│ comprehensive       │ "Here's my detailed assessment..."
│ analysis to human   │
└─────────┬───────────┘
          │
          ▼
      ┌───┴───┐
      │ Human │ 👨‍💼 HUMAN DECISION POINT
      │Choice?│ (2 options)
      └───┬───┘
          │
    ┌─────┴─────┐
    │           │
    ▼           ▼
┌─────────┐ ┌─────────┐
│ Proceed │ │   Fix   │ 👨‍💼 HUMAN OPTIONS
│         │ │Milestone│
└────┬────┘ └────┬────┘
     │           │
     │           ▼
     │      ┌─────────────┐
     │      │ Human       │ 👨‍💼 ASYNC IMPROVEMENT
     │      │ improves    │
     │      │ milestone   │
     │      │ based on    │
     │      │ detailed    │
     │      │ feedback    │
     │      └─────┬───────┘
     │            │
     │            ▼
     │       ┌─────────┐
     │       │ RESTART │ 🔄 LOOP BACK
     │       │ PROCESS │
     │       └─────────┘
     │
     ▼
╔═════════════════════╗
║ PHASE 1:            ║ 🤖 AGENT AUTOMATION
║ INSTRUCTION         ║
║ GENERATION          ║
║                     ║
║ • Use existing      ║
║   generator         ║
║ • Create agent-     ║
║   specific          ║
║   instructions      ║
╚═════════┬═══════════╝
          │
          ▼
╔═════════════════════╗
║ PHASE 3:            ║ 🤖 AGENT EXECUTION START
║ EXECUTION START     ║
║                     ║
║ • Read instructions ║
║ • Setup environment ║
║ • Validate readiness║
╚═════════┬═══════════╝
          │
          ▼
╔═════════════════════╗
║ PHASE 4:            ║ 🤖 AGENT TASK LOOP
║ TASK EXECUTION      ║
║                     ║ ┌─────────────────┐
║ FOR EACH TASK:      ║ │ 👨‍💼 ASYNC       │
║ • Create branch     ║ │ INTERVENTION    │
║ • Work on task      ║ │                 │
║ • Update logs       ║ │ Human can:      │
║ • Merge & cleanup   ║ │ • Send messages │
║ • Move to next      ║ │ • Modify state  │
╚═════════┬═══════════╝ │ • Override      │
          │             │   behavior      │
          │             └─────────────────┘
          ▼                       │
    ┌─────────────┐               │
    │ Check for   │ 🤖 MESSAGE CHECK POINT
    │ human       │               │
    │ messages    │               │
    └─────┬───────┘               │
          │                       │
          ▼                       │
      ┌───────┐                   │
      │ Any   │                   │
      │ msgs? │                   │
      └───┬───┘                   │
          │                       │
      ┌───┴───┐                   │
      │  Yes  │                   │
      └───┬───┘                   │
          │                       │
          ▼                       │
    ┌─────────────┐               │
    │ Process     │ 🤖 AGENT ADAPTS
    │ human       │               │
    │ guidance    │               │
    └─────┬───────┘               │
          │                       │
          └───────────────────────┘
          │
      ┌───┴───┐
      │  No   │
      └───┬───┘
          │
          ▼
    ┌─────────────┐
    │ Continue    │ 🤖 CONTINUE EXECUTION
    │ with next   │
    │ task        │
    └─────┬───────┘
          │
          ▼
      ┌───────┐
      │ More  │
      │ tasks?│
      └───┬───┘
          │
      ┌───┴───┐
      │  Yes  │ ──────┐
      └───────┘       │
          │           │
      ┌───┴───┐       │
      │  No   │       │
      └───┬───┘       │
          │           │
          ▼           │
╔═════════════════════╗ │
║ PHASE 5:            ║ │ 🤖 AGENT FINALIZATION
║ FINALIZATION        ║ │
║                     ║ │
║ • Run acceptance    ║ │
║   tests             ║ │
║ • Final validation  ║ │
║ • Merge to main     ║ │
║ • Update docs       ║ │
╚═════════┬═══════════╝ │
          │             │
          ▼             │
┌─────────────────────┐ │
│ SUCCESS!            │ │ ✅ COMPLETION
│ Milestone complete  │ │
└─────────────────────┘ │
                        │
          ┌─────────────┘
          │
          ▼
╔═════════════════════╗
║ RECOVERY PHASE      ║ 🚨 ERROR HANDLING
║ (if errors occur)   ║ (Available at any point)
║                     ║
║ • Diagnose issue    ║ 👨‍💼 HUMAN INTERVENTION
║ • Offer recovery    ║ Human can:
║   options           ║ • Guide recovery
║ • Get human help    ║ • Reset state
╚═════════════════════╝ • Manual override
```

### Phase Flow Summary
1. **Phase 0**: Milestone analysis & improvement (always detailed)
2. **Phase 1**: Instruction generation (uses existing tools)
3. **Phase 3**: Execution start (setup and validation)
4. **Phase 4**: Task execution (loop with human intervention points)
5. **Phase 5**: Finalization (acceptance tests and cleanup)

### State Management
- State stored in: `.milestone-state/{milestone-id}/`
- Persistent message history
- Automatic recovery capabilities
- Manual state editing supported

## 🎯 Key Features

### ✅ Solves Core Problems
- **Ensures milestone reading**: Phase 0 forces analysis and validation
- **Enforces branch management**: Automatic task branch creation/cleanup
- **Requires work log updates**: Blocks progression until logs updated

### ✅ Human-AI Collaboration
- **Async messaging**: Send guidance anytime, agent checks at checkpoints
- **Question/answer flow**: Agent can ask for clarification
- **Manual overrides**: Edit state files for fine control
- **Recovery assistance**: Multiple recovery options when things go wrong

### ✅ Leverages Existing Tools
- Uses your instruction generator
- Follows your git workflow
- Uses your work log templates
- Runs your acceptance tests

## 📖 Detailed Usage

### Agent Workflow
```bash
# Start milestone execution
./milestone-guide.sh M1.2 augment

# Script guides through:
# 1. Milestone analysis (with human review)
# 2. Instruction generation
# 3. Task-by-task execution
# 4. Work log validation
# 5. Git branch management
# 6. Final acceptance tests
```

### Human Oversight
```bash
# Monitor progress
./milestone-control.sh M1.2 status

# Send real-time guidance
./milestone-control.sh M1.2 message "Try the simpler approach"

# View conversation history
./milestone-control.sh M1.2 messages

# Control execution
./milestone-control.sh M1.2 pause
./milestone-control.sh M1.2 resume
./milestone-control.sh M1.2 reset 3
```

## 🔧 State Files

### Current State (`.milestone-state/M1.2/current-state.json`)
```json
{
  "milestone_id": "M1.2",
  "agent_type": "augment",
  "current_phase": "task_execution",
  "current_task": 3,
  "total_tasks": 18,
  "started_at": "2025-01-03T10:30:00Z",
  "last_updated": "2025-01-03T14:15:00Z",
  "phase_history": [...]
}
```

### Messages (`.milestone-state/M1.2/human-messages.json`)
```json
{
  "messages": [
    {
      "id": "1704285000",
      "timestamp": "2025-01-03T14:30:00Z",
      "from": "human",
      "to": "agent",
      "message": "Focus on error handling, skip optimization",
      "status": "acknowledged"
    }
  ]
}
```

## 🚨 Recovery Options

### Automatic Recovery
```bash
# Agent detects issues and offers options:
# 1) Reset to start of current task
# 2) Reset to previous task
# 3) Reset to milestone start
# 4) Manual recovery help
```

### Manual Recovery
```bash
# Edit state file directly
vim .milestone-state/M1.2/current-state.json

# Reset via control script
./milestone-control.sh M1.2 reset 3

# Send recovery guidance
./milestone-control.sh M1.2 message "Reset to task 2 and try different approach"
```

## 📁 File Structure

```
.milestone-state/
├── M1.2/
│   ├── current-state.json      # Execution state
│   └── human-messages.json     # Conversation history
└── archived/                   # Completed milestones

work-log/
└── milestone-M1.2/
    ├── instructions-for-augment.md
    ├── implementation-log.md
    ├── technical-reference.md
    ├── conversation-summary.md
    └── fixes-checklist.md
```

## 🎯 Success Metrics

### Before Automation
- Agents skip milestone reading: ~80%
- Wrong branch usage: ~60%
- Missing work logs: ~90%

### After Automation
- Milestone reading: 100% (enforced)
- Correct branching: 100% (automated)
- Work log updates: 100% (required)

## 💡 Tips

### For Agents
- Always run the same command: `./milestone-guide.sh M1.2`
- Script remembers where you are and continues appropriately
- Check for human messages at each checkpoint
- Use recovery options when stuck

### For Humans
- Monitor with `status` command regularly
- Send guidance proactively, not just when problems occur
- Use `messages` to review full conversation history
- Edit state files for fine-grained control when needed

## 🧪 Testing Checklist

Use this checklist to validate the scripts against the flowchart:

### Phase 0 Testing
```bash
# Test Phase 0A: Quick Analysis
./milestone-guide.sh M1.2 augment
# ✅ Should extract task count, success criteria, deliverables
# ✅ Should automatically continue to detailed analysis

# Test Phase 0B: Detailed Analysis
# ✅ Should run spec-lint validation
# ✅ Should identify concerns (high task count, missing sections, etc.)
# ✅ Should present comprehensive analysis to human
# ✅ Should offer 2 choices: Proceed or Fix

# Test Human Decision Point
# Choice 1: Proceed → Should go to Phase 1
# Choice 2: Fix → Should exit gracefully for milestone improvement
```

### Phase 1 Testing
```bash
# Test Instruction Generation
# ✅ Should create work-log directory structure
# ✅ Should call existing instruction generator
# ✅ Should generate instructions-for-{agent}.md
# ✅ Should automatically proceed to Phase 3 (no Phase 2)
```

### Phase 3 Testing
```bash
# Test Execution Start
# ✅ Should validate instructions file exists
# ✅ Should show pre-execution checklist
# ✅ Should require human confirmation before proceeding
# ✅ Should proceed to Phase 4 on confirmation
```

### Phase 4 Testing
```bash
# Test Task Execution Loop
# ✅ Should check for human messages at start
# ✅ Should create milestone branch if needed
# ✅ Should create task-specific branch
# ✅ Should show current task info
# ✅ Should wait for task completion
# ✅ Should validate work logs are updated
# ✅ Should commit and merge task work
# ✅ Should increment to next task or go to Phase 5
```

### Human Intervention Testing
```bash
# Test Async Messaging
./milestone-control.sh M1.2 message "Test guidance"
# ✅ Should create/update human-messages.json
# ✅ Agent should see message at next checkpoint
# ✅ Agent should acknowledge message

# Test Status Monitoring
./milestone-control.sh M1.2 status
# ✅ Should show current phase, task, progress
# ✅ Should show git status
# ✅ Should show recent activity
# ✅ Should show unread messages

# Test Question/Answer Flow
# Agent asks question → creates message with to="human"
./milestone-control.sh M1.2 answer "Test answer"
# ✅ Should mark agent questions as answered
# ✅ Should send answer to agent
```

### Recovery Testing
```bash
# Test Recovery Options
# Simulate error state, then run:
./milestone-guide.sh M1.2 augment
# ✅ Should detect invalid state and enter recovery mode
# ✅ Should offer 4 recovery options
# ✅ Should reset to chosen point and resume normal flow

# Test Manual Recovery
./milestone-control.sh M1.2 reset 3
# ✅ Should reset state to task 3
# ✅ Should allow resumption from that point
```

### State Management Testing
```bash
# Test State Persistence
# Start milestone, interrupt, restart
# ✅ Should resume from correct phase/task
# ✅ Should preserve message history
# ✅ Should maintain phase history

# Test Manual State Editing
# Edit .milestone-state/M1.2/current-state.json
# ✅ Agent should respect manual state changes
# ✅ Should continue from edited state
```

### Integration Testing
```bash
# Test Full End-to-End Flow
# ✅ Phase 0: Analysis → Human review → Proceed
# ✅ Phase 1: Instruction generation
# ✅ Phase 3: Execution start
# ✅ Phase 4: Complete all tasks with work log updates
# ✅ Phase 5: Acceptance tests and finalization
# ✅ Human intervention: Send messages during execution
# ✅ Recovery: Test recovery from various failure points
```

## 🔍 Troubleshooting

### Common Issues
1. **"Milestone file not found"**: Check milestone file path and name
2. **"Instruction generator failed"**: Ensure node and generator script exist
3. **"Git operations failed"**: Check git repository state and permissions
4. **"State file corrupted"**: Delete state directory and restart

### Debug Commands
```bash
# Check state
cat .milestone-state/M1.2/current-state.json

# Check messages
cat .milestone-state/M1.2/human-messages.json

# Manual state reset
rm -rf .milestone-state/M1.2
```

### Validation Against Flowchart
For each test, trace through the flowchart to ensure:
1. **Correct phase transitions**: Each phase leads to expected next phase
2. **Human intervention points**: Messages work at designated checkpoints
3. **State updates**: State file reflects current position in flowchart
4. **Recovery paths**: All recovery options lead back to normal flow
5. **Error handling**: Failures trigger appropriate recovery mechanisms

## 🎉 Success!

When everything works correctly:
- Milestones execute reliably and consistently
- Human-AI collaboration is smooth and effective
- Work logs are complete and up-to-date
- Git history is clean and organized
- Acceptance tests pass automatically
- Recovery from issues is quick and easy
