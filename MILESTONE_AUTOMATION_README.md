# Milestone Automation Scripts

Automated milestone execution with human-AI collaboration.

## 🚀 Quick Start

### For Agents (Primary Interface)
```bash
# Execute a milestone
./milestone-guide.sh M1.2 augment
```

### For Humans (Oversight Interface)
```bash
# Check status
./milestone-control.sh M1.2 status

# Send guidance
./milestone-control.sh M1.2 message "Focus on error handling, skip optimization"

# Answer agent questions
./milestone-control.sh M1.2 answer "Use the regex approach"
```

## 📋 How It Works

### Phase Flow
1. **Phase 0**: Milestone analysis & improvement
2. **Phase 1**: Instruction generation  
3. **Phase 3**: Execution start
4. **Phase 4**: Task execution (loop)
5. **Phase 5**: Finalization

### State Management
- State stored in: `.milestone-state/{milestone-id}/`
- Persistent message history
- Automatic recovery capabilities
- Manual state editing supported

## 🎯 Key Features

### ✅ Solves Core Problems
- **Ensures milestone reading**: Phase 0 forces analysis and validation
- **Enforces branch management**: Automatic task branch creation/cleanup
- **Requires work log updates**: Blocks progression until logs updated

### ✅ Human-AI Collaboration
- **Async messaging**: Send guidance anytime, agent checks at checkpoints
- **Question/answer flow**: Agent can ask for clarification
- **Manual overrides**: Edit state files for fine control
- **Recovery assistance**: Multiple recovery options when things go wrong

### ✅ Leverages Existing Tools
- Uses your instruction generator
- Follows your git workflow
- Uses your work log templates
- Runs your acceptance tests

## 📖 Detailed Usage

### Agent Workflow
```bash
# Start milestone execution
./milestone-guide.sh M1.2 augment

# Script guides through:
# 1. Milestone analysis (with human review)
# 2. Instruction generation
# 3. Task-by-task execution
# 4. Work log validation
# 5. Git branch management
# 6. Final acceptance tests
```

### Human Oversight
```bash
# Monitor progress
./milestone-control.sh M1.2 status

# Send real-time guidance
./milestone-control.sh M1.2 message "Try the simpler approach"

# View conversation history
./milestone-control.sh M1.2 messages

# Control execution
./milestone-control.sh M1.2 pause
./milestone-control.sh M1.2 resume
./milestone-control.sh M1.2 reset 3
```

## 🔧 State Files

### Current State (`.milestone-state/M1.2/current-state.json`)
```json
{
  "milestone_id": "M1.2",
  "agent_type": "augment",
  "current_phase": "task_execution",
  "current_task": 3,
  "total_tasks": 18,
  "started_at": "2025-01-03T10:30:00Z",
  "last_updated": "2025-01-03T14:15:00Z",
  "phase_history": [...]
}
```

### Messages (`.milestone-state/M1.2/human-messages.json`)
```json
{
  "messages": [
    {
      "id": "1704285000",
      "timestamp": "2025-01-03T14:30:00Z",
      "from": "human",
      "to": "agent",
      "message": "Focus on error handling, skip optimization",
      "status": "acknowledged"
    }
  ]
}
```

## 🚨 Recovery Options

### Automatic Recovery
```bash
# Agent detects issues and offers options:
# 1) Reset to start of current task
# 2) Reset to previous task  
# 3) Reset to milestone start
# 4) Manual recovery help
```

### Manual Recovery
```bash
# Edit state file directly
vim .milestone-state/M1.2/current-state.json

# Reset via control script
./milestone-control.sh M1.2 reset 3

# Send recovery guidance
./milestone-control.sh M1.2 message "Reset to task 2 and try different approach"
```

## 📁 File Structure

```
.milestone-state/
├── M1.2/
│   ├── current-state.json      # Execution state
│   └── human-messages.json     # Conversation history
└── archived/                   # Completed milestones

work-log/
└── milestone-M1.2/
    ├── instructions-for-augment.md
    ├── implementation-log.md
    ├── technical-reference.md
    ├── conversation-summary.md
    └── fixes-checklist.md
```

## 🎯 Success Metrics

### Before Automation
- Agents skip milestone reading: ~80%
- Wrong branch usage: ~60%  
- Missing work logs: ~90%

### After Automation
- Milestone reading: 100% (enforced)
- Correct branching: 100% (automated)
- Work log updates: 100% (required)

## 💡 Tips

### For Agents
- Always run the same command: `./milestone-guide.sh M1.2`
- Script remembers where you are and continues appropriately
- Check for human messages at each checkpoint
- Use recovery options when stuck

### For Humans
- Monitor with `status` command regularly
- Send guidance proactively, not just when problems occur
- Use `messages` to review full conversation history
- Edit state files for fine-grained control when needed

## 🔍 Troubleshooting

### Common Issues
1. **"Milestone file not found"**: Check milestone file path and name
2. **"Instruction generator failed"**: Ensure node and generator script exist
3. **"Git operations failed"**: Check git repository state and permissions
4. **"State file corrupted"**: Delete state directory and restart

### Debug Commands
```bash
# Check state
cat .milestone-state/M1.2/current-state.json

# Check messages
cat .milestone-state/M1.2/human-messages.json

# Manual state reset
rm -rf .milestone-state/M1.2
```

## 🎉 Success!

When everything works correctly:
- Milestones execute reliably and consistently
- Human-AI collaboration is smooth and effective
- Work logs are complete and up-to-date
- Git history is clean and organized
- Acceptance tests pass automatically
- Recovery from issues is quick and easy
