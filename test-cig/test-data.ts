/**
 * Test data representing current M2 annotation scenarios
 */

export const testScenarios = [
  // Scenario 1: Current M2 duplicate issue - AuditSchema
  {
    name: "M2 AuditSchema Duplicates",
    annotations: [
      {
        annotation: "@implements milestone-M2#AuditSchema",
        filePath: "auditReport.ts",
        functionName: "generateAuditReport",
        description: "Creates audit report structure"
      },
      {
        annotation: "@implements milestone-M2#AuditSchema",
        filePath: "auditReport.ts", 
        functionName: "validateAuditReport",
        description: "Validates report against schema"
      }
    ],
    expectedBehavior: "Should create implementation graph with 2 functions implementing AuditSchema"
  },

  // Scenario 2: Current M2 duplicate issue - Coverage
  {
    name: "M2 Coverage Duplicates",
    annotations: [
      {
        annotation: "@implements milestone-M2#Coverage",
        filePath: "coverage.ts",
        functionName: "calculateAuditCoverage",
        description: "Calculates single milestone coverage"
      },
      {
        annotation: "@implements milestone-M2#Coverage",
        filePath: "coverage.ts",
        functionName: "calculateAllMilestoneCoverage", 
        description: "Calculates coverage for all milestones"
      }
    ],
    expectedBehavior: "Should create implementation graph with 2 functions implementing Coverage"
  },

  // Scenario 3: Current M2 duplicate issue - AuditLib
  {
    name: "M2 AuditLib Duplicates",
    annotations: [
      {
        annotation: "@implements milestone-M2#AuditLib",
        filePath: "index.ts",
        functionName: "initializeAuditLibrary",
        description: "Library setup and configuration"
      },
      {
        annotation: "@implements milestone-M2#AuditLib",
        filePath: "types.ts",
        functionName: "validateAuditTypes",
        description: "Type validation for library"
      }
    ],
    expectedBehavior: "Should create implementation graph with 2 functions implementing AuditLib"
  },

  // Scenario 4: Cross-milestone dependencies (M1.2 -> M2)
  {
    name: "Cross-Milestone Dependencies",
    annotations: [
      {
        annotation: "@implements milestone-M1.2#GraphUpdateCore",
        filePath: "updateGraph.ts",
        functionName: "updateGraph",
        description: "Core graph update functionality"
      },
      {
        annotation: "@implements milestone-M2#AuditReporting\n@depends milestone-M1.2#GraphUpdateCore",
        filePath: "auditReport.ts",
        functionName: "generateReport",
        description: "Audit reporting that depends on graph updates"
      }
    ],
    expectedBehavior: "Should track dependency relationship between milestones"
  },

  // Scenario 5: Test annotations
  {
    name: "Test Coverage Annotations",
    annotations: [
      {
        annotation: "@tests milestone-M2#AuditSchema",
        filePath: "auditReport.test.ts",
        functionName: "testAuditReportGeneration",
        description: "Tests for audit report functionality"
      },
      {
        annotation: "@tests milestone-M2#Coverage",
        filePath: "coverage.test.ts",
        functionName: "testCoverageCalculation",
        description: "Tests for coverage calculation"
      }
    ],
    expectedBehavior: "Should track test coverage for components"
  },

  // Scenario 6: Hierarchical components
  {
    name: "Hierarchical Components",
    annotations: [
      {
        annotation: "@implements milestone-M2#AuditSystem",
        filePath: "auditSystem.ts",
        functionName: "AuditSystem",
        description: "Main audit system class"
      },
      {
        annotation: "@implements milestone-M2#AuditSystem.ReportGenerator",
        filePath: "reportGenerator.ts",
        functionName: "ReportGenerator",
        description: "Report generation subsystem"
      },
      {
        annotation: "@implements milestone-M2#AuditSystem.Validator",
        filePath: "validator.ts",
        functionName: "Validator",
        description: "Validation subsystem"
      }
    ],
    expectedBehavior: "Should handle hierarchical component structure"
  },

  // Scenario 7: Versioned components
  {
    name: "Component Versioning",
    annotations: [
      {
        annotation: "@implements milestone-M2#AuditSchema@v1.0",
        filePath: "auditReportV1.ts",
        functionName: "generateAuditReportV1",
        description: "Legacy audit report generation"
      },
      {
        annotation: "@implements milestone-M2#AuditSchema@v2.0",
        filePath: "auditReportV2.ts",
        functionName: "generateAuditReportV2",
        description: "Current audit report generation"
      }
    ],
    expectedBehavior: "Should track component versions"
  },

  // Scenario 8: Multi-language implementations
  {
    name: "Multi-Language Implementation",
    annotations: [
      {
        annotation: "@implements milestone-M2#DataProcessor",
        filePath: "processor.ts",
        functionName: "processDataTS",
        description: "TypeScript data processor"
      },
      {
        annotation: "@implements milestone-M2#DataProcessor",
        filePath: "processor.py",
        functionName: "process_data_py",
        description: "Python data processor"
      },
      {
        annotation: "@implements milestone-M2#DataProcessor",
        filePath: "processor.js",
        functionName: "processDataJS",
        description: "JavaScript data processor"
      }
    ],
    expectedBehavior: "Should track implementations across languages"
  }
];

export const expectedResults = {
  "M2 AuditSchema Duplicates": {
    implementationCount: 2,
    averageConfidence: 1.0,
    isComplete: true,
    healthScore: 0.67 // 2/3 implementations
  },
  "M2 Coverage Duplicates": {
    implementationCount: 2,
    averageConfidence: 1.0,
    isComplete: true,
    healthScore: 0.67
  },
  "M2 AuditLib Duplicates": {
    implementationCount: 2,
    averageConfidence: 1.0,
    isComplete: true,
    healthScore: 0.67
  },
  "Cross-Milestone Dependencies": {
    implementationCount: 1,
    dependencyCount: 1,
    crossMilestoneDependencies: true
  },
  "Test Coverage Annotations": {
    testCount: 2,
    testedComponents: ["milestone-M2#AuditSchema", "milestone-M2#Coverage"]
  },
  "Multi-Language Implementation": {
    implementationCount: 3,
    languageCount: 3,
    languages: ["typescript", "python", "javascript"]
  }
};
