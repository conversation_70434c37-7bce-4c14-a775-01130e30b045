/**
 * Test runner for Component Implementation Graph prototype
 */

import { ComponentImplementationGraph, parseTestAnnotation } from './prototype';
import { testScenarios, expectedResults } from './test-data';

interface TestResult {
  scenario: string;
  passed: boolean;
  details: any;
  error?: string;
}

function runTests(): TestResult[] {
  const results: TestResult[] = [];
  const graph = new ComponentImplementationGraph();

  console.log('🧪 Testing Component Implementation Graph Prototype\n');

  // Process all test scenarios
  for (const scenario of testScenarios) {
    console.log(`📋 Testing: ${scenario.name}`);
    
    try {
      // Parse annotations and add to graph
      for (const annotation of scenario.annotations) {
        const edges = parseTestAnnotation(
          annotation.annotation,
          annotation.filePath,
          annotation.functionName
        );
        
        for (const edge of edges) {
          graph.addEdge(edge);
        }
      }

      // Test specific scenarios
      const testResult = testScenario(scenario, graph);
      results.push(testResult);
      
      console.log(`   ${testResult.passed ? '✅' : '❌'} ${testResult.passed ? 'PASSED' : 'FAILED'}`);
      if (!testResult.passed && testResult.error) {
        console.log(`   Error: ${testResult.error}`);
      }
      
    } catch (error) {
      results.push({
        scenario: scenario.name,
        passed: false,
        details: {},
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ FAILED - ${error}`);
    }
    
    console.log('');
  }

  // Print overall statistics
  printOverallStats(graph);
  
  // Print summary
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  console.log(`\n📊 Test Summary: ${passed}/${total} scenarios passed`);
  
  return results;
}

function testScenario(scenario: any, graph: ComponentImplementationGraph): TestResult {
  const scenarioName = scenario.name;
  
  switch (scenarioName) {
    case "M2 AuditSchema Duplicates":
      return testDuplicateImplementations(scenario, graph, "component:milestone-M2#AuditSchema");
      
    case "M2 Coverage Duplicates":
      return testDuplicateImplementations(scenario, graph, "component:milestone-M2#Coverage");
      
    case "M2 AuditLib Duplicates":
      return testDuplicateImplementations(scenario, graph, "component:milestone-M2#AuditLib");
      
    case "Cross-Milestone Dependencies":
      return testCrossMilestoneDependencies(scenario, graph);
      
    case "Test Coverage Annotations":
      return testTestAnnotations(scenario, graph);
      
    case "Hierarchical Components":
      return testHierarchicalComponents(scenario, graph);
      
    case "Component Versioning":
      return testComponentVersioning(scenario, graph);
      
    case "Multi-Language Implementation":
      return testMultiLanguageImplementation(scenario, graph);
      
    default:
      return {
        scenario: scenarioName,
        passed: false,
        details: {},
        error: "Unknown test scenario"
      };
  }
}

function testDuplicateImplementations(scenario: any, graph: ComponentImplementationGraph, componentId: string): TestResult {
  const implementations = graph.getComponentImplementations(componentId);
  const metrics = graph.calculateComponentMetrics(componentId);
  
  const expectedCount = scenario.annotations.length;
  const actualCount = implementations.length;
  
  const passed = actualCount === expectedCount && metrics.isComplete;
  
  return {
    scenario: scenario.name,
    passed,
    details: {
      expectedImplementations: expectedCount,
      actualImplementations: actualCount,
      metrics,
      implementations: implementations.map(impl => ({
        source: impl.source,
        relationship: impl.relationship,
        confidence: impl.metadata.confidence
      }))
    },
    error: passed ? undefined : `Expected ${expectedCount} implementations, got ${actualCount}`
  };
}

function testCrossMilestoneDependencies(scenario: any, graph: ComponentImplementationGraph): TestResult {
  // Look for dependency relationships
  const allComponents = graph.getAllComponents();
  let dependencyFound = false;
  
  for (const componentId of allComponents) {
    const implementations = graph.getComponentImplementations(componentId);
    for (const impl of implementations) {
      if (impl.relationship === 'depends') {
        dependencyFound = true;
        break;
      }
    }
  }
  
  return {
    scenario: scenario.name,
    passed: dependencyFound,
    details: {
      dependencyFound,
      allComponents
    },
    error: dependencyFound ? undefined : "No dependency relationships found"
  };
}

function testTestAnnotations(scenario: any, graph: ComponentImplementationGraph): TestResult {
  const allComponents = graph.getAllComponents();
  let testRelationshipsFound = 0;
  
  for (const componentId of allComponents) {
    const implementations = graph.getComponentImplementations(componentId);
    for (const impl of implementations) {
      if (impl.relationship === 'tests') {
        testRelationshipsFound++;
      }
    }
  }
  
  const expectedTests = scenario.annotations.length;
  const passed = testRelationshipsFound === expectedTests;
  
  return {
    scenario: scenario.name,
    passed,
    details: {
      expectedTests,
      actualTests: testRelationshipsFound
    },
    error: passed ? undefined : `Expected ${expectedTests} test relationships, got ${testRelationshipsFound}`
  };
}

function testHierarchicalComponents(scenario: any, graph: ComponentImplementationGraph): TestResult {
  const allComponents = graph.getAllComponents();
  const hierarchicalComponents = allComponents.filter(c => c.includes('.'));
  
  const expectedHierarchical = scenario.annotations.filter(a => a.annotation.includes('.')).length;
  const actualHierarchical = hierarchicalComponents.length;
  
  const passed = actualHierarchical === expectedHierarchical;
  
  return {
    scenario: scenario.name,
    passed,
    details: {
      expectedHierarchical,
      actualHierarchical,
      hierarchicalComponents
    },
    error: passed ? undefined : `Expected ${expectedHierarchical} hierarchical components, got ${actualHierarchical}`
  };
}

function testComponentVersioning(scenario: any, graph: ComponentImplementationGraph): TestResult {
  const allComponents = graph.getAllComponents();
  let versionedImplementations = 0;
  
  for (const componentId of allComponents) {
    const implementations = graph.getComponentImplementations(componentId);
    for (const impl of implementations) {
      if (impl.metadata.version) {
        versionedImplementations++;
      }
    }
  }
  
  const expectedVersioned = scenario.annotations.filter(a => a.annotation.includes('@')).length;
  const passed = versionedImplementations === expectedVersioned;
  
  return {
    scenario: scenario.name,
    passed,
    details: {
      expectedVersioned,
      actualVersioned: versionedImplementations
    },
    error: passed ? undefined : `Expected ${expectedVersioned} versioned implementations, got ${versionedImplementations}`
  };
}

function testMultiLanguageImplementation(scenario: any, graph: ComponentImplementationGraph): TestResult {
  const componentId = "component:milestone-M2#DataProcessor";
  const implementations = graph.getComponentImplementations(componentId);
  const metrics = graph.calculateComponentMetrics(componentId);
  
  const languages = new Set(implementations.map(impl => impl.metadata.language));
  const expectedLanguages = 3; // typescript, python, javascript
  
  const passed = languages.size === expectedLanguages && implementations.length === 3;
  
  return {
    scenario: scenario.name,
    passed,
    details: {
      expectedLanguages,
      actualLanguages: languages.size,
      languages: Array.from(languages),
      implementations: implementations.length,
      metrics
    },
    error: passed ? undefined : `Expected ${expectedLanguages} languages, got ${languages.size}`
  };
}

function printOverallStats(graph: ComponentImplementationGraph) {
  const stats = graph.getStats();
  
  console.log('📊 Overall Graph Statistics:');
  console.log(`   Total Components: ${stats.totalComponents}`);
  console.log(`   Total Implementations: ${stats.totalImplementations}`);
  console.log(`   Avg Implementations per Component: ${stats.averageImplementationsPerComponent.toFixed(2)}`);
  console.log(`   Components with Multiple Implementations: ${stats.componentsWithMultipleImplementations}`);
  
  // Show detailed breakdown
  console.log('\n📋 Component Details:');
  for (const componentId of graph.getAllComponents()) {
    const implementations = graph.getComponentImplementations(componentId);
    const metrics = graph.calculateComponentMetrics(componentId);
    
    console.log(`   ${componentId}:`);
    console.log(`     Implementations: ${implementations.length}`);
    console.log(`     Health Score: ${metrics.healthScore.toFixed(2)}`);
    console.log(`     Languages: ${Array.from(metrics.languageDistribution.keys()).join(', ')}`);
    
    // Show relationships
    const relationships = new Set(implementations.map(impl => impl.relationship));
    console.log(`     Relationships: ${Array.from(relationships).join(', ')}`);
  }
}

// Run the tests
if (require.main === module) {
  runTests();
}

export { runTests };
