/**
 * Minimal Component Implementation Graph Prototype
 * Test the core concept before full implementation
 */

interface ComponentEdge {
  source: string;      // "function:file.ts#functionName"
  target: string;      // "component:milestone-M2#AuditSchema"
  relationship: string; // "implements" | "depends" | "tests"
  metadata: {
    confidence: number;
    language: string;
    lastVerified: string;
    version?: string;
    deprecated?: boolean;
  };
}

interface ComponentMetrics {
  implementationCount: number;
  averageConfidence: number;
  languageDistribution: Map<string, number>;
  isComplete: boolean;
  healthScore: number;
}

class ComponentImplementationGraph {
  private edges: Map<string, ComponentEdge[]> = new Map();

  addEdge(edge: ComponentEdge) {
    const targetEdges = this.edges.get(edge.target) || [];
    targetEdges.push(edge);
    this.edges.set(edge.target, targetEdges);
  }

  getComponentImplementations(componentId: string): ComponentEdge[] {
    return this.edges.get(componentId) || [];
  }

  calculateComponentMetrics(componentId: string): ComponentMetrics {
    const implementations = this.getComponentImplementations(componentId);
    
    if (implementations.length === 0) {
      return {
        implementationCount: 0,
        averageConfidence: 0,
        languageDistribution: new Map(),
        isComplete: false,
        healthScore: 0
      };
    }

    const languageDistribution = new Map<string, number>();
    let totalConfidence = 0;

    for (const impl of implementations) {
      totalConfidence += impl.metadata.confidence;
      const lang = impl.metadata.language;
      languageDistribution.set(lang, (languageDistribution.get(lang) || 0) + 1);
    }

    const averageConfidence = totalConfidence / implementations.length;
    const healthScore = this.calculateHealthScore(implementations);

    return {
      implementationCount: implementations.length,
      averageConfidence,
      languageDistribution,
      isComplete: implementations.length >= 2, // Arbitrary threshold for testing
      healthScore
    };
  }

  private calculateHealthScore(implementations: ComponentEdge[]): number {
    // Simple health score: average confidence * implementation count factor
    const avgConfidence = implementations.reduce((sum, impl) => sum + impl.metadata.confidence, 0) / implementations.length;
    const countFactor = Math.min(implementations.length / 3, 1); // Diminishing returns after 3 implementations
    return avgConfidence * countFactor;
  }

  getAllComponents(): string[] {
    return Array.from(this.edges.keys());
  }

  getStats() {
    const components = this.getAllComponents();
    const totalImplementations = Array.from(this.edges.values()).reduce((sum, impls) => sum + impls.length, 0);
    
    return {
      totalComponents: components.length,
      totalImplementations,
      averageImplementationsPerComponent: totalImplementations / components.length || 0,
      componentsWithMultipleImplementations: components.filter(c => this.getComponentImplementations(c).length > 1).length
    };
  }
}

// Simple annotation parser for testing
function parseTestAnnotation(annotation: string, filePath: string, functionName: string): ComponentEdge[] {
  const edges: ComponentEdge[] = [];
  const lines = annotation.split('\n').filter(line => line.trim().startsWith('@'));

  for (const line of lines) {
    const implementsMatch = line.match(/@implements\s+([^\s@]+)(?:@([^\s]+))?/);
    const dependsMatch = line.match(/@depends\s+([^\s@]+)/);
    const testsMatch = line.match(/@tests\s+([^\s@]+)/);

    if (implementsMatch) {
      edges.push({
        source: `function:${filePath}#${functionName}`,
        target: `component:${implementsMatch[1]}`,
        relationship: 'implements',
        metadata: {
          confidence: 1.0,
          language: filePath.endsWith('.ts') ? 'typescript' : 'javascript',
          lastVerified: new Date().toISOString(),
          version: implementsMatch[2]
        }
      });
    }

    if (dependsMatch) {
      edges.push({
        source: `function:${filePath}#${functionName}`,
        target: `component:${dependsMatch[1]}`,
        relationship: 'depends',
        metadata: {
          confidence: 0.8,
          language: filePath.endsWith('.ts') ? 'typescript' : 'javascript',
          lastVerified: new Date().toISOString()
        }
      });
    }

    if (testsMatch) {
      edges.push({
        source: `function:${filePath}#${functionName}`,
        target: `component:${testsMatch[1]}`,
        relationship: 'tests',
        metadata: {
          confidence: 0.9,
          language: filePath.endsWith('.ts') ? 'typescript' : 'javascript',
          lastVerified: new Date().toISOString()
        }
      });
    }
  }

  return edges;
}

export { ComponentImplementationGraph, parseTestAnnotation, ComponentEdge, ComponentMetrics };
