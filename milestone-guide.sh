#!/bin/bash

# Milestone Guide Script
# Automates milestone execution workflow with human-AI collaboration
# Usage: ./milestone-guide.sh <milestone-id> [agent-type] [--autonomous]

# ═══════════════════════════════════════════════════════════════════════════════
# 📋 SCRIPT FLOW CHART - How to Read This Script Line by Line
# ═══════════════════════════════════════════════════════════════════════════════
#
# ENTRY POINT:
# main() [line ~80] → validate_dependencies() [line ~20] → load_state() [line ~160]
#
# PHASE ROUTING (based on CURRENT_PHASE):
# main() → case "$CURRENT_PHASE" in:
#   ├── "not_started"      → phase_pre_review() [line ~240]
#   ├── "instructions_ready" → phase_execution_start() [line ~540]
#   ├── "execution_started" → phase_task_execution() [line ~590]
#   ├── "task_execution"   → phase_task_execution() [line ~590]
#   ├── "milestone_done"   → phase_finalization() [line ~880]
#   └── *                  → phase_recovery() [line ~950]
#
# PHASE 0: PRE-REVIEW WORKFLOW
# phase_pre_review() [line ~240]:
#   ├── perform_quick_analysis() [line ~330] (deterministic: spec-lint, metrics)
#   ├── perform_detailed_analysis() [line ~350] (AI-powered: generates analysis prompt)
#   ├── Decision Mode:
#   │   ├── HUMAN MODE: Manual choice (1=proceed, 2=fix)
#   │   │   ├── 1 → update_state("instruction_generation") → phase_instruction_generation()
#   │   │   └── 2 → exit (human improves milestone)
#   │   └── AUTONOMOUS MODE: AI agent decision process
#   │       ├── AI agent analyzes detailed analysis prompt with repository context
#   │       ├── Assesses implementation confidence (1-10 scale)
#   │       ├── If confidence ≥ 7/10: proceed to phase_instruction_generation()
#   │       ├── If confidence < 7/10: exit for AI agent to improve milestone
#   │       ├── Retry tracking: up to 3 attempts with milestone improvements
#   │       └── Max retries reached: proceed anyway
#
# PHASE 1: INSTRUCTION GENERATION WORKFLOW
# phase_instruction_generation() [line ~450]:
#   ├── setup_work_environment() [line ~480]
#   ├── Call existing instruction-generator.mjs
#   └── update_state("instructions_ready") → phase_execution_start()
#
# PHASE 3: EXECUTION START WORKFLOW
# phase_execution_start() [line ~540]:
#   ├── Validate instructions file exists
#   ├── Pre-execution checklist
#   ├── Human confirmation (or autonomous auto-proceed)
#   └── update_state("task_execution", 1) → phase_task_execution()
#
# PHASE 4: TASK EXECUTION WORKFLOW (MAIN LOOP)
# phase_task_execution() [line ~590]:
#   ├── check_human_messages() [line ~230]
#   │   ├── Process unread messages
#   │   ├── check_if_stuck_and_ask_for_help() [line ~270]
#   │   └── Autonomous mode handling
#   ├── validate_git_state() [line ~650]
#   ├── show_current_task_info() [line ~670]
#   ├── setup_task_branch() [line ~680] (git branch management)
#   ├── Human: "Work on task, press Enter when done"
#   ├── complete_current_task() [line ~720]:
#   │   ├── validate_work_logs() [line ~730] (blocks until logs updated)
#   │   ├── commit_task_work() [line ~780] (git commit)
#   │   ├── merge_task_to_milestone() [line ~800] (git merge & cleanup)
#   │   └── Move to next task OR phase_finalization()
#   └── Loop back to phase_task_execution() for next task
#
# PHASE 5: FINALIZATION WORKFLOW
# phase_finalization() [line ~880]:
#   ├── run_acceptance_tests() [line ~900]
#   ├── Final validation checklist
#   ├── Human confirmation (or autonomous auto-proceed)
#   ├── finalize_milestone() [line ~930]
#   └── cleanup_milestone_state() [line ~950] (optional)
#
# RECOVERY WORKFLOW (ERROR HANDLING)
# phase_recovery() [line ~970]:
#   ├── diagnose_current_state() [line ~1000]
#   ├── Human choice (1=current task, 2=previous task, 3=start, 4=manual)
#   │   ├── 1 → reset_to_current_task() [line ~1030]
#   │   ├── 2 → reset_to_previous_task() [line ~1040]
#   │   ├── 3 → reset_to_milestone_start() [line ~1050]
#   │   └── 4 → show_manual_recovery_help() [line ~1070]
#   └── Resume normal execution from reset point
#
# HELPER FUNCTIONS (called throughout):
# ├── State Management:
# │   ├── load_state() [line ~160] - Load current execution state
# │   ├── validate_state_file() [line ~130] - Check state file integrity
# │   ├── update_state() [line ~180] - Update phase/task and save
# │   └── initialize_state() [line ~200] - Create new state file
# ├── Message System:
# │   ├── check_human_messages() [line ~230] - Process human guidance
# │   ├── ask_human_question() [line ~310] - Send question to human
# │   └── add_message() [line ~320] - Add message to persistent log
# ├── Git Operations:
# │   ├── validate_git_state() [line ~650] - Check git repository status
# │   ├── setup_task_branch() [line ~680] - Create/switch to task branch
# │   ├── commit_task_work() [line ~780] - Commit task changes
# │   └── merge_task_to_milestone() [line ~800] - Merge task to milestone branch
# └── Analysis Functions:
#     ├── perform_quick_analysis() [line ~330] - Extract basic milestone info + spec-lint validation
#     └── perform_detailed_analysis() [line ~350] - AI-powered implementation confidence assessment
#
# AUTONOMOUS MODE BEHAVIOR:
# When --autonomous flag is used:
# ├── PRE-REVIEW: AI agent analyzes detailed analysis prompt and makes intelligent decisions
# │   ├── Assesses implementation confidence (1-10 scale) based on repository context
# │   ├── If confidence ≥ 7/10: proceeds automatically to instruction generation
# │   ├── If confidence < 7/10: exits for AI agent to improve milestone (up to 3 retries)
# │   └── Max retries reached: proceeds anyway to prevent infinite loops
# ├── EXECUTION: All human input prompts auto-proceed with defaults
# ├── MESSAGES: Auto-acknowledged and stuck detection automatically asks for help
# ├── SETUP: Confirmations are skipped for streamlined workflow
# └── TESTING: Acceptance test failures are ignored in autonomous mode
#
# ERROR PATHS:
# ├── Missing dependencies → validate_dependencies() → exit with install guide
# ├── Corrupted state file → validate_state_file() → recovery options
# ├── Missing milestone file → phase_pre_review() → exit with error
# ├── Instruction generation fails → phase_instruction_generation() → exit
# ├── Git operations fail → improved error messages with recovery guidance
# ├── Work log validation fails → validate_work_logs() → blocks until fixed
# ├── Acceptance tests fail → run_acceptance_tests() → human choice
# └── Unknown state → phase_recovery() → manual intervention
#
# ═══════════════════════════════════════════════════════════════════════════════

set -euo pipefail

# Global variables - initialized in main()
MILESTONE_ID=""
AGENT_TYPE=""
AUTONOMOUS_MODE=""
STATE_DIR=""
STATE_FILE=""
MESSAGES_FILE=""
MILESTONE_FILE=""
INSTRUCTIONS_FILE=""

# Initialize script
main() {
    if [[ $# -lt 1 ]]; then
        echo "Usage: $0 <milestone-id> [agent-type] [--autonomous]"
        echo "Example: $0 M1.2 augment"
        echo "Example: $0 M1.2 augment --autonomous"
        echo ""
        echo "Options:"
        echo "  --autonomous    Skip human input prompts (auto-proceed with defaults)"
        exit 1
    fi

    # Initialize variables with defaults
    MILESTONE_ID="$1"
    AGENT_TYPE="${2:-augment}"
    AUTONOMOUS_MODE="false"

    # Check for autonomous mode flag
    if [[ "${3:-}" == "--autonomous" ]] || [[ "${2:-}" == "--autonomous" ]]; then
        AUTONOMOUS_MODE="true"
        echo "🤖 Running in autonomous mode (minimal human interaction)"
    fi

    # Validate dependencies first
    validate_dependencies

    # Setup paths
    STATE_DIR="docs/tech-specs/milestones/state/$MILESTONE_ID"
    STATE_FILE="$STATE_DIR/current-state.json"
    MESSAGES_FILE="$STATE_DIR/human-messages.json"
    MILESTONE_FILE="docs/tech-specs/milestones/milestone-$MILESTONE_ID.mdx"
    INSTRUCTIONS_FILE="work-log/milestone-$MILESTONE_ID/instructions-for-$AGENT_TYPE.md"

    # Create state directory
    mkdir -p "$STATE_DIR"

    # Load or initialize state
    load_state

    # Route based on current phase
    case "$CURRENT_PHASE" in
        "not_started")      phase_pre_review ;;
        "instructions_ready") phase_execution_start ;;
        "execution_started") phase_task_execution ;;
        "task_execution")   phase_task_execution ;;
        "milestone_done")   phase_finalization ;;
        *)                  phase_recovery ;;
    esac
}

# Validate dependencies
validate_dependencies() {
    local missing_deps=()

    # Check for required commands
    # Note: jq is used for robust JSON parsing/manipulation in bash
    # While agents could parse JSON manually, jq provides:
    # - Safe JSON validation and error handling
    # - Atomic file updates (read->modify->write)
    # - Complex JSON queries and transformations
    # - Consistent behavior across different shell environments
    if ! command -v jq >/dev/null; then
        missing_deps+=("jq (JSON processor - enables robust state management)")
    fi

    if ! command -v node >/dev/null; then
        missing_deps+=("node (JavaScript runtime)")
    fi

    if ! command -v git >/dev/null; then
        missing_deps+=("git (version control)")
    fi

    # Check for required files
    if [[ ! -f "code/scripts/spec-lint.mjs" ]]; then
        missing_deps+=("code/scripts/spec-lint.mjs (spec validation)")
    fi

    if [[ ! -f "docs/scripts/instruction-generator.mjs" ]]; then
        missing_deps+=("docs/scripts/instruction-generator.mjs (instruction generator)")
    fi

    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        echo "❌ Missing required dependencies:"
        printf '  - %s\n' "${missing_deps[@]}"
        echo ""
        echo "💡 Installation suggestions:"

        # Provide specific installation commands
        for dep in "${missing_deps[@]}"; do
            case "$dep" in
                "jq (JSON processor)")
                    echo "  📦 Install jq:"
                    echo "    • macOS: brew install jq"
                    echo "    • Ubuntu/Debian: sudo apt-get install jq"
                    echo "    • CentOS/RHEL: sudo yum install jq"
                    ;;
                "node (JavaScript runtime)")
                    echo "  📦 Install Node.js:"
                    echo "    • Visit: https://nodejs.org/"
                    echo "    • Or use nvm: curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
                    ;;
                "git (version control)")
                    echo "  📦 Install Git:"
                    echo "    • macOS: brew install git"
                    echo "    • Ubuntu/Debian: sudo apt-get install git"
                    echo "    • Windows: https://git-scm.com/download/win"
                    ;;
                *"spec-lint.mjs"*)
                    echo "  📄 Missing spec-lint.mjs:"
                    echo "    • Ensure you're in the correct repository root"
                    echo "    • File should be at: code/scripts/spec-lint.mjs"
                    ;;
                *"instruction-generator.mjs"*)
                    echo "  📄 Missing instruction-generator.mjs:"
                    echo "    • Ensure you're in the correct repository root"
                    echo "    • File should be at: docs/scripts/instruction-generator.mjs"
                    ;;
            esac
        done

        echo ""
        echo "💡 After installing dependencies, run this script again"
        exit 1
    fi
}
load_state() {
    # Validate state file first
    validate_state_file

    if [[ -f "$STATE_FILE" ]]; then
        CURRENT_PHASE=$(jq -r '.current_phase // "not_started"' "$STATE_FILE")
        CURRENT_TASK=$(jq -r '.current_task // 1' "$STATE_FILE")
        TOTAL_TASKS=$(jq -r '.total_tasks // 0' "$STATE_FILE")
    else
        CURRENT_PHASE="not_started"
        CURRENT_TASK=1
        TOTAL_TASKS=0
        initialize_state
    fi
}

# State management functions
validate_state_file() {
    if [[ ! -f "$STATE_FILE" ]]; then
        return 0  # File doesn't exist, will be created
    fi

    # Check if file is valid JSON
    if ! jq empty "$STATE_FILE" 2>/dev/null; then
        echo "⚠️ State file is corrupted or invalid JSON: $STATE_FILE"
        echo "💡 Recovery options:"
        echo "  1. Delete state file and restart: rm '$STATE_FILE'"
        echo "  2. Restore from backup: ls '$STATE_FILE'.backup.*"
        echo "  3. Manual repair: edit '$STATE_FILE' to fix JSON syntax"
        echo ""
        read -p "Delete corrupted state file and restart? (y/n): " delete_state

        if [[ "${delete_state,,}" == "y" ]]; then
            rm "$STATE_FILE"
            echo "✅ Corrupted state file deleted. Restarting fresh."
            return 0
        else
            echo "❌ Cannot proceed with corrupted state file."
            exit 1
        fi
    fi

    # Check for required fields
    local required_fields=("milestone_id" "current_phase" "current_task")
    for field in "${required_fields[@]}"; do
        if ! jq -e ".$field" "$STATE_FILE" >/dev/null 2>&1; then
            echo "⚠️ State file missing required field: $field"
            echo "💡 Consider deleting state file to restart: rm '$STATE_FILE'"
            return 1
        fi
    done

    return 0
}

initialize_state() {
    cat > "$STATE_FILE" << EOF
{
  "milestone_id": "$MILESTONE_ID",
  "agent_type": "$AGENT_TYPE",
  "current_phase": "not_started",
  "current_task": 1,
  "total_tasks": 0,
  "started_at": "$(date -Iseconds)",
  "last_updated": "$(date -Iseconds)",
  "phase_history": []
}
EOF
}

update_state() {
    local new_phase="$1"
    local task_num="${2:-$CURRENT_TASK}"

    # Update state file
    jq --arg phase "$new_phase" \
       --arg task "$task_num" \
       --arg timestamp "$(date -Iseconds)" \
       '.current_phase = $phase |
        .current_task = ($task | tonumber) |
        .last_updated = $timestamp |
        .phase_history += [{phase: $phase, task: ($task | tonumber), timestamp: $timestamp}]' \
       "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

    # Update global variables
    CURRENT_PHASE="$new_phase"
    CURRENT_TASK="$task_num"
}

# Message handling functions
check_human_messages() {
    if [[ ! -f "$MESSAGES_FILE" ]]; then
        return 0
    fi

    # Check for unread messages to agent
    local unread_count=$(jq '[.messages[] | select(.to == "agent" and .status == "unread")] | length' "$MESSAGES_FILE")

    if [[ "$unread_count" -gt 0 ]]; then
        echo ""
        echo "💬 You have $unread_count new message(s) from human:"
        echo "================================================"

        # Show unread messages
        jq -r '.messages[] | select(.to == "agent" and .status == "unread") | "[\(.timestamp)] \(.message)"' "$MESSAGES_FILE"

        echo ""
        if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
            echo "🤖 Autonomous mode: Auto-acknowledging messages"
            ack_response="y"
        else
            read -p "Acknowledge these messages? (y/n): " ack_response
        fi

        if [[ "$ack_response" == "y" ]]; then
            # Mark as acknowledged
            jq '(.messages[] | select(.to == "agent" and .status == "unread") | .status) = "acknowledged"' \
                "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
            echo "✅ Messages acknowledged"
        else
            # Mark as read but not acknowledged
            jq '(.messages[] | select(.to == "agent" and .status == "unread") | .status) = "read"' \
                "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
            echo "📖 Messages marked as read. I'll ask again later."
        fi
    fi

    # Check if we might be stuck and should ask for help
    check_if_stuck_and_ask_for_help
}

check_if_stuck_and_ask_for_help() {
    # Only check during task execution
    if [[ "$CURRENT_PHASE" != "task_execution" ]]; then
        return 0
    fi

    # Get last update time
    local last_updated=$(jq -r '.last_updated // "unknown"' "$STATE_FILE")
    if [[ "$last_updated" == "unknown" ]]; then
        return 0
    fi

    # Calculate time since last update
    local last_epoch=$(date -d "$last_updated" +%s 2>/dev/null || echo "0")
    local current_epoch=$(date +%s)
    local duration_minutes=$(( (current_epoch - last_epoch) / 60 ))

    # Check if we might be stuck
    if [[ $duration_minutes -gt 30 ]]; then
        echo ""
        echo "🤔 I notice I haven't made progress in $duration_minutes minutes..."
        echo "💡 This might indicate I'm stuck on task $CURRENT_TASK"

        # Ask for help automatically in autonomous mode, or suggest it in interactive mode
        if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
            ask_human_question "I appear to be stuck on task $CURRENT_TASK for $duration_minutes minutes. Could you provide guidance or suggest a different approach?"
        else
            echo ""
            echo "🆘 Consider asking for human help:"
            echo "  ./milestone-control.sh $MILESTONE_ID message 'Agent stuck on task $CURRENT_TASK for $duration_minutes minutes'"
            echo ""
            read -p "Should I continue anyway? (y/n): " continue_stuck
            if [[ "$continue_stuck" != "y" ]]; then
                echo "⏸️ Pausing for human intervention..."
                exit 0
            fi
        fi
    fi
}

ask_human_question() {
    local question="$1"
    local question_id="$(date +%s)"

    echo ""
    echo "❓ I need clarification:"
    echo "$question"
    echo ""

    # DESIGN NOTE: Agent Instantiation Limitation (addressing your feedback)
    # You're absolutely correct about the concurrency issue:
    # - This script instance is currently running and waiting
    # - Human needs to use milestone-control.sh to respond
    # - But this agent can't check for responses while waiting for user input
    #
    # CURRENT SOLUTION: Agent asks question, exits, human responds, agent restarts
    # FUTURE SOLUTIONS:
    # 1. Background message checking with signal handling
    # 2. Separate agent instances for monitoring vs execution
    # 3. Event-driven architecture with message queues

    # Add question to messages
    add_message "agent" "human" "$question" "unread"

    echo "💡 I've added this question to the message log."
    echo "You can answer using: ./milestone-control.sh $MILESTONE_ID answer \"your response\""
    echo ""
    echo "🔄 IMPORTANT: This script will now exit so you can respond."
    echo "   After responding, restart with: $0 $MILESTONE_ID $AGENT_TYPE"
}

add_message() {
    local from="$1"
    local to="$2"
    local message="$3"
    local status="$4"

    local new_message=$(jq -n \
        --arg id "$(date +%s)" \
        --arg timestamp "$(date -Iseconds)" \
        --arg from "$from" \
        --arg to "$to" \
        --arg message "$message" \
        --arg status "$status" \
        '{
            id: $id,
            timestamp: $timestamp,
            from: $from,
            to: $to,
            message: $message,
            status: $status
        }')

    if [[ -f "$MESSAGES_FILE" ]]; then
        jq ".messages += [$new_message]" "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
    else
        echo "{\"messages\": [$new_message]}" > "$MESSAGES_FILE"
    fi
}

# Phase 0: Pre-review and milestone analysis
phase_pre_review() {
    echo "🔍 Milestone Pre-Review & Analysis"
    echo "=================================="
    echo ""

    # Validate milestone file exists
    if [[ ! -f "$MILESTONE_FILE" ]]; then
        echo "❌ Milestone file not found: $MILESTONE_FILE"
        echo ""
        echo "Available milestones:"
        ls docs/tech-specs/milestones/milestone-*.mdx 2>/dev/null || echo "No milestone files found"
        exit 1
    fi

    echo "📖 Analyzing milestone: $MILESTONE_FILE"
    echo ""

    # Phase 0A: Quick analysis
    echo "📊 Quick Analysis:"
    echo "=================="
    perform_quick_analysis

    echo ""
    echo "🔍 Detailed Analysis:"
    echo "===================="
    perform_detailed_analysis

    if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
        echo ""
        echo "🤖 Autonomous mode: AI agent making decision based on detailed analysis..."

        # Get current retry count from state
        local retry_count=$(jq -r '.retry_count // 0' "$STATE_FILE")
        local max_retries=3

        echo "📊 Retry attempt: $((retry_count + 1))/$max_retries"
        echo ""
        echo "🧠 AI Agent Decision Process:"
        echo "=============================="
        echo "As an AI agent, I will now analyze the milestone using the detailed analysis prompt"
        echo "and make an intelligent decision"
        echo ""
        echo "📋 Analysis prompt available at: docs/tech-specs/milestones/state/milestone-${MILESTONE_ID}-analysis-prompt.md"
        echo ""

        # AI agent should analyze and decide
        echo "🎯 AI AGENT DECISION REQUIRED:"
        echo "Based on the detailed analysis above, I need to decide:"
        echo "  1) Proceed with execution (confidence ≥ 9/10)"
        echo "  2) Improve milestone first (confidence < 9/10)"
        echo ""

        if [[ $retry_count -ge $max_retries ]]; then
            echo "⏭️ Max retries reached ($retry_count/$max_retries) - proceeding anyway"
            review_choice="1"
            # Reset retry count
            jq '.retry_count = 0' "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"
        else
            echo "� As the AI agent running this script, I should:"
            echo "   • Review the analysis prompt and milestone specification"
            echo "   • Assess implementation confidence (1-10 scale)"
            echo "   • If confidence < 7: improve the milestone and retry"
            echo "   • If confidence ≥ 7: proceed with execution"
            echo ""
            echo "� If I choose to improve the milestone:"
            echo "   • Edit the milestone file based on analysis recommendations"
            echo "   • Run this script again: $0 $MILESTONE_ID $AGENT_TYPE"
            echo ""
            echo "✅ If I'm confident in the milestone:"
            echo "   • Continue with instruction generation and execution"
            echo ""

            # Increment retry count for tracking
            jq --arg count "$((retry_count + 1))" '.retry_count = ($count | tonumber)' \
               "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

            # Let AI agent decide - exit so agent can analyze and choose
            echo "⏸️ Pausing for AI agent decision..."
            echo "💡 AI Agent: Please analyze the milestone and choose your next action."
            exit 0
        fi
    else
        echo ""
        echo "💬 Human Review Required:"
        echo "========================"
        echo "Based on my analysis above, please choose:"
        echo ""
        echo "  1) Proceed with execution (milestone ready)"
        echo "  2) Fix milestone based on my recommendations"
        echo ""
        read -p "Your choice (1-2): " review_choice
    fi

    case "$review_choice" in
        1)
            echo "✅ Proceeding to instruction generation..."
            update_state "instruction_generation"
            phase_instruction_generation
            ;;
        *)
            echo "📝 Please improve milestone based on detailed analysis above."
            echo "💡 Run this script again when ready: $0 $MILESTONE_ID $AGENT_TYPE"
            exit 0
            ;;
    esac
}

perform_quick_analysis() {
   # Run spec-lint validation as part of quick analysis
    echo ""
    echo "🔧 Running spec-lint validation..."
    if command -v node >/dev/null && [[ -f "code/scripts/spec-lint.mjs" ]]; then
        if node code/scripts/spec-lint.mjs "$MILESTONE_FILE"; then
            echo "✅ Milestone passes spec-lint validation"
        else
            echo "❌ Milestone has spec-lint errors"
            echo "💡 Please fix these issues before proceeding"
            exit 1
        fi
    else
        echo "⚠️ spec-lint not available (node or code/scripts/spec-lint.mjs missing), skipping validation"
    fi
}

perform_detailed_analysis() {
    echo "📋 AI-Powered Implementation Confidence Assessment:"
    echo ""

    # Create a comprehensive analysis prompt for the AI agent
    local analysis_prompt="docs/tech-specs/milestones/state/milestone-${MILESTONE_ID}-analysis-prompt.md"

    echo "🤖 Generating contextual analysis prompt..."

    cat > "$analysis_prompt" << EOF
# Implementation Confidence Assessment for Milestone $MILESTONE_ID

## Context
You are an expert software engineer reviewing this milestone specification for implementation feasibility.

## Your Task
Analyze the milestone specification with full repository context and provide:

1. **Implementation Confidence Score** (1-10 scale):
   - 1-3: High risk, significant concerns
   - 4-6: Moderate risk, some concerns
   - 7-8: Good confidence, minor concerns
   - 9-10: High confidence, ready to implement

2. **Detailed Assessment**:
   - **Feasibility**: Are the tasks technically achievable with current codebase?
   - **Scope**: Is the milestone appropriately sized and scoped?
   - **Dependencies**: Are external dependencies realistic and available?
   - **Complexity**: Does task complexity match available time/resources?
   - **Clarity**: Are requirements clear enough for implementation?

3. **Specific Concerns** (if any):
   - Technical blockers or challenges
   - Missing information or unclear requirements
   - Unrealistic timelines or scope
   - Dependency issues or conflicts

4. **Recommendations**:
   - What should be fixed before implementation?
   - What additional information is needed?
   - How to improve milestone quality?

## Analysis Guidelines
- Consider the current repository structure and capabilities
- Evaluate task breakdown against actual codebase complexity
- Assess if deliverables align with technical architecture
- Check if acceptance criteria are testable and realistic
- Evaluate available tooling and dependencies in the codebase

## Quick Analysis Results
From automated analysis:
- Success Criteria: $(grep -c "^[0-9]\+\.\s\*\*" "$MILESTONE_FILE" || echo "0") items
- Tasks: $TOTAL_TASKS items
- Deliverables: $(grep -c "|\s*[^|]*\.(js\|ts\|md\|json)" "$MILESTONE_FILE" || echo "0") items

## Milestone Specification
\`\`\`markdown
$(cat "$MILESTONE_FILE")
\`\`\`

---

**Please provide your confidence assessment and detailed analysis above.**
EOF

    echo "📄 Analysis prompt created: $analysis_prompt"
    echo ""
    echo "🎯 NEXT STEP: AI Agent Analysis Required"
    echo "======================================"
    echo ""
    echo "To get intelligent implementation confidence assessment:"
    echo ""
    echo "1. **Open the analysis prompt**: $analysis_prompt"
    echo "2. **Share with your AI agent** (Claude, GPT, etc.)"
    echo "3. **Get contextual assessment** based on full repository understanding"
    echo "4. **Review the confidence score and recommendations**"
    echo ""
    echo "💡 This replaces deterministic rule-checking with intelligent analysis"
    echo "💡 The AI agent will consider actual codebase complexity and feasibility"
    echo ""
    echo "⚠️  **Manual Review Recommended**: Even with AI analysis, human judgment is valuable"
}

# Phase 1: Instruction generation
# Note: Phase 2 (Instruction Review) was eliminated - we skip directly to Phase 3
phase_instruction_generation() {
    echo ""
    echo "📝 Phase 1: Generating Execution Instructions"
    echo "============================================="
    echo "💡 Note: Phase 2 (Instruction Review) was eliminated for streamlined workflow"

    # Setup work environment
    setup_work_environment

    # Generate instructions using existing tool
    echo "🔧 Using existing instruction generator..."

    if command -v node >/dev/null && [[ -f "docs/scripts/instruction-generator.mjs" ]]; then
        if node docs/scripts/instruction-generator.mjs \
            "$MILESTONE_FILE" \
            "$AGENT_TYPE" \
            "$INSTRUCTIONS_FILE"; then

            echo "✅ Instructions generated: $INSTRUCTIONS_FILE"
            update_state "instructions_ready"
            phase_execution_start
        else
            echo "❌ Failed to generate instructions"
            echo "💡 The milestone may have issues that weren't caught in Phase 0"
            exit 1
        fi
    else
        echo "❌ Instruction generator not available"
        echo "💡 Please ensure node and docs/scripts/instruction-generator.mjs exist"
        exit 1
    fi
}

setup_work_environment() {
    local work_dir="work-log/milestone-$MILESTONE_ID"

    echo "🛠️ Setting up work environment..."

    # Create work directory
    mkdir -p "$work_dir"

    # Initialize work log files if they don't exist
    if [[ ! -f "$work_dir/implementation-log.md" ]]; then
        cat > "$work_dir/implementation-log.md" << EOF
# Implementation Log - Milestone $MILESTONE_ID

## Overview
Implementation log for milestone $MILESTONE_ID started on $(date +%Y-%m-%d).

## Task Progress
EOF
    fi

    if [[ ! -f "$work_dir/technical-reference.md" ]]; then
        cat > "$work_dir/technical-reference.md" << EOF
# Technical Reference - Milestone $MILESTONE_ID

## APIs and Interfaces

## Key Technical Decisions

## Dependencies
EOF
    fi

    if [[ ! -f "$work_dir/conversation-summary.md" ]]; then
        cat > "$work_dir/conversation-summary.md" << EOF
# Conversation Summary - Milestone $MILESTONE_ID

## Questions and Clarifications

## Decisions Made
EOF
    fi

    if [[ ! -f "$work_dir/fixes-checklist.md" ]]; then
        cat > "$work_dir/fixes-checklist.md" << EOF
# Fixes Checklist - Milestone $MILESTONE_ID

## Issues Encountered

## Resolutions Applied
EOF
    fi

    echo "✅ Work environment ready: $work_dir"
}

# Phase 3: Execution start
# Note: We skip Phase 2 (Instruction Review) and go directly from Phase 1 to Phase 3
phase_execution_start() {
    echo ""
    echo "🚀 Phase 3: Starting Milestone Execution"
    echo "========================================"
    echo "💡 Note: Skipped Phase 2 (Instruction Review) for streamlined workflow"

    echo "📋 Following instructions from: $INSTRUCTIONS_FILE"
    echo ""

    # Validate instructions exist
    if [[ ! -f "$INSTRUCTIONS_FILE" ]]; then
        echo "❌ Instructions file not found: $INSTRUCTIONS_FILE"
        echo "💡 Try regenerating instructions"
        exit 1
    fi

    echo "🛑 Pre-Execution Setup:"
    echo "======================="
    echo "Before starting task execution, ensure you have:"
    echo "  ✓ Read the instructions thoroughly"
    echo "  ✓ Understood the milestone requirements"
    echo "  ✓ Set up your development environment"
    echo "  ✓ Have access to all required tools and dependencies"
    echo ""

    if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
        echo "🤖 Autonomous mode: Auto-proceeding with task execution"
        setup_complete="y"
    else
        read -p "Have you completed all setup steps above? (y/n): " setup_complete
    fi

    if [[ "$setup_complete" == "y" ]]; then
        echo "✅ Setup complete. Starting task execution..."
        update_state "task_execution" 1
        phase_task_execution
    else
        echo "⚠️ Please complete setup steps first."
        echo "💡 Refer to: $INSTRUCTIONS_FILE"
        echo "💡 Run this script again when ready: $0 $MILESTONE_ID $AGENT_TYPE"
        exit 0
    fi
}

# Phase 4: Task execution
phase_task_execution() {
    echo ""
    echo "🔨 Task Execution Phase"
    echo "======================"

    # Check for human messages first
    check_human_messages

    # Validate git state
    validate_git_state

    echo "Current progress: Task $CURRENT_TASK of $TOTAL_TASKS"

    if [[ $CURRENT_TASK -gt $TOTAL_TASKS ]]; then
        echo "🎉 All tasks completed!"
        update_state "milestone_done"
        phase_finalization
        return
    fi

    echo ""
    echo "📋 Task $CURRENT_TASK Details:"
    show_current_task_info

    # Check if we're on the right branch
    setup_task_branch

    echo ""
    echo "🎯 Work on your task now."
    echo "When you're done, run: $0 $MILESTONE_ID $AGENT_TYPE"
    echo ""
    echo "💡 Remember to:"
    echo "  - Follow the instructions in: $INSTRUCTIONS_FILE"
    echo "  - Update work logs as you progress"
    echo "  - Commit your changes regularly"
    echo ""

    # Wait for user to complete task
    read -p "Press Enter when task is complete, or 'q' to quit: " task_status

    if [[ "$task_status" == "q" ]]; then
        echo "⏸️ Execution paused. Run script again to continue."
        exit 0
    fi

    # Task completion workflow
    complete_current_task
}

validate_git_state() {
    if ! command -v git >/dev/null; then
        echo "⚠️ Git not available. Skipping git validation."
        return
    fi

    if [[ ! -d ".git" ]]; then
        echo "⚠️ Not in a git repository. Skipping git validation."
        return
    fi

    # Check for uncommitted changes in wrong location
    if ! git diff --quiet && [[ $(git branch --show-current) == "main" ]]; then
        echo "⚠️ You have uncommitted changes on main branch"
        echo "💡 Consider committing or stashing these changes"
    fi
}

show_current_task_info() {
    # Try to extract task info from milestone file
    local task_line=$(sed -n "${CURRENT_TASK}p" <(grep "|\s*[0-9]\+\s*|" "$MILESTONE_FILE" | head -n $TOTAL_TASKS))

    if [[ -n "$task_line" ]]; then
        echo "Task details from milestone spec:"
        echo "$task_line"
    else
        echo "Task $CURRENT_TASK (refer to milestone spec for details)"
    fi
}

setup_task_branch() {
    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        echo "⚠️ Git not available. Skipping branch management."
        return
    fi

    local milestone_branch="milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    local task_branch="$milestone_branch/task-$(printf "%02d" $CURRENT_TASK)"
    local current_branch=$(git branch --show-current)

    echo "🔧 Git branch management:"

    # Ensure milestone branch exists
    if ! git show-ref --verify --quiet "refs/heads/$milestone_branch"; then
        echo "  Creating milestone branch: $milestone_branch"
        git checkout -b "$milestone_branch" 2>/dev/null || echo "  ⚠️ Could not create milestone branch"
    fi

    # Create or switch to task branch
    if [[ "$current_branch" != "$task_branch" ]]; then
        echo "  Switching to task branch: $task_branch"
        git checkout "$milestone_branch" 2>/dev/null || true
        git checkout -b "$task_branch" 2>/dev/null || git checkout "$task_branch" 2>/dev/null || echo "  ⚠️ Could not manage task branch"
    else
        echo "  ✅ Already on correct branch: $current_branch"
    fi
}

complete_current_task() {
    echo ""
    echo "✅ Task Completion Check"
    echo "======================="

    # Check for human messages
    check_human_messages

    # Validate work logs are updated
    validate_work_logs

    # Commit any remaining changes
    commit_task_work

    # Merge task back to milestone branch
    merge_task_to_milestone

    # Move to next task
    local next_task=$((CURRENT_TASK + 1))

    if [[ $next_task -le $TOTAL_TASKS ]]; then
        echo "🎯 Ready for task $next_task"
        update_state "task_execution" $next_task
        echo "Run: $0 $MILESTONE_ID $AGENT_TYPE"
    else
        echo "🎉 All tasks complete! Moving to finalization."
        update_state "milestone_done"
        phase_finalization
    fi
}

validate_work_logs() {
    local work_dir="work-log/milestone-$MILESTONE_ID"
    local log_file="$work_dir/implementation-log.md"

    echo "📝 Checking work log updates..."

    # Check if task completion is documented using flexible patterns
    local task_documented=false

    # Try multiple completion patterns
    if grep -q -i "task\s*$CURRENT_TASK.*\(completed\|complete\|done\|finished\|✅\)" "$log_file" 2>/dev/null; then
        task_documented=true
    elif grep -q -i "task\s*$CURRENT_TASK:" "$log_file" 2>/dev/null && grep -A 5 -i "task\s*$CURRENT_TASK:" "$log_file" | grep -q -i "\(completed\|complete\|done\|finished\|✅\)" 2>/dev/null; then
        task_documented=true
    elif grep -q "###.*[Tt]ask\s*$CURRENT_TASK" "$log_file" 2>/dev/null; then
        task_documented=true
    fi

    if [[ "$task_documented" == "false" ]]; then
        echo "❌ Work logs not updated for task $CURRENT_TASK"
        echo ""
        echo "Please add an entry to: $log_file"
        echo "Accepted formats:"
        echo "  - ### Task $CURRENT_TASK: COMPLETED"
        echo "  - Task $CURRENT_TASK completed"
        echo "  - Task $CURRENT_TASK: done"
        echo "  - ✅ Task $CURRENT_TASK finished"
        echo ""
        echo "Example entry:"
        echo "### Task $CURRENT_TASK: COMPLETED"
        echo "- Description: [what you accomplished]"
        echo "- Files modified: [list files]"
        echo "- Key decisions: [any important decisions]"
        echo ""

        # Robust input validation with retries
        local max_retries=3
        local retry_count=0

        while [[ $retry_count -lt $max_retries ]]; do
            read -p "Have you updated the work logs? (y/n): " logs_updated

            case "${logs_updated,,}" in  # Convert to lowercase
                y|yes)
                    echo "✅ Proceeding with work log validation confirmed"
                    break
                    ;;
                n|no)
                    echo "⚠️ Please update work logs before continuing."
                    exit 1
                    ;;
                *)
                    ((retry_count++))
                    if [[ $retry_count -lt $max_retries ]]; then
                        echo "❌ Invalid input. Please enter 'y' for yes or 'n' for no. (Attempt $retry_count/$max_retries)"
                    else
                        echo "❌ Too many invalid attempts. Assuming 'no' - please update work logs."
                        exit 1
                    fi
                    ;;
            esac
        done
    else
        echo "✅ Work logs appear to be updated for task $CURRENT_TASK"
    fi
}

commit_task_work() {
    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        return
    fi

    echo "💾 Checking for uncommitted changes..."

    if ! git diff --quiet || ! git diff --cached --quiet; then
        echo "📝 Found uncommitted changes"

        read -p "Commit these changes? (y/n): " commit_changes

        if [[ "$commit_changes" == "y" ]]; then
            git add .
            git commit -m "feat(task-$CURRENT_TASK): Complete task $CURRENT_TASK implementation

- Task $CURRENT_TASK completed as part of milestone $MILESTONE_ID
- See work logs for detailed changes and decisions" || echo "⚠️ Commit failed"
            echo "✅ Changes committed"
        else
            echo "⚠️ Uncommitted changes remain"
        fi
    else
        echo "✅ No uncommitted changes"
    fi
}

merge_task_to_milestone() {
    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        echo "⚠️ Git not available, skipping branch merge"
        return
    fi

    local milestone_branch="milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    local task_branch="$milestone_branch/task-$(printf "%02d" $CURRENT_TASK)"
    local current_branch=$(git branch --show-current)

    echo "🔄 Merging task to milestone branch..."

    if [[ "$current_branch" == "$task_branch" ]]; then
        # Switch to milestone branch with error handling
        if ! git checkout "$milestone_branch"; then
            echo "❌ Failed to switch to milestone branch: $milestone_branch"
            echo "💡 You may need to manually merge task branch: $task_branch"
            return 1
        fi

        # Merge with error handling
        if ! git merge --squash "$task_branch"; then
            echo "❌ Failed to merge task branch: $task_branch"
            echo "💡 You may need to resolve conflicts manually"
            return 1
        fi

        # Commit with error handling
        if ! git commit -m "feat(milestone-$MILESTONE_ID): Complete task $CURRENT_TASK"; then
            echo "❌ Failed to commit merge (may be nothing to commit)"
            echo "💡 Check git status and commit manually if needed"
        fi

        # Delete task branch with confirmation
        if git branch -d "$task_branch"; then
            echo "✅ Task merged and branch cleaned up"
        else
            echo "⚠️ Could not delete task branch: $task_branch"
            echo "💡 You may need to delete it manually: git branch -D $task_branch"
        fi
    else
        echo "⚠️ Not on expected task branch ($task_branch), currently on: $current_branch"
        echo "💡 Skipping automatic merge - please merge manually if needed"
    fi
}

# Phase 5: Finalization
phase_finalization() {
    echo ""
    echo "🎉 Milestone Finalization"
    echo "========================"

    echo "🎯 All tasks completed for milestone $MILESTONE_ID!"
    echo ""

    # Run acceptance tests if available
    run_acceptance_tests

    # Final validation
    echo "🔍 Final validation checklist:"
    echo "  ✓ All $TOTAL_TASKS tasks completed"
    echo "  ✓ Work logs updated"
    echo "  ✓ Code committed and merged"

    # Check if acceptance tests exist and run them
    local acceptance_script="docs/scripts/acceptance/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')-acceptance.sh"
    if [[ -f "$acceptance_script" ]]; then
        echo "  ✓ Acceptance tests available"
    else
        echo "  ⚠️ No acceptance tests found at: $acceptance_script"
    fi

    echo ""
    read -p "Ready to finalize milestone? (y/n): " finalize_ready

    if [[ "$finalize_ready" == "y" ]]; then
        finalize_milestone
    else
        echo "⏸️ Finalization paused. Run script again when ready."
        exit 0
    fi
}

run_acceptance_tests() {
    # Try multiple acceptance script naming patterns
    local milestone_lower=$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')
    local acceptance_scripts=(
        "docs/scripts/acceptance/${milestone_lower}-acceptance.sh"
        "docs/scripts/acceptance/milestone-${MILESTONE_ID}.sh"
        "docs/scripts/acceptance/milestone-${milestone_lower}.sh"
    )

    echo "🧪 Running acceptance tests..."

    local script_found=false
    for acceptance_script in "${acceptance_scripts[@]}"; do
        if [[ -f "$acceptance_script" ]]; then
            script_found=true
            echo "Found acceptance tests: $acceptance_script"

            if bash "$acceptance_script"; then
                echo "✅ Acceptance tests passed!"
                return 0
            else
                echo "❌ Acceptance tests failed!"
                echo "💡 Please review and fix issues before finalizing"

                if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
                    echo "🤖 Autonomous mode: Continuing despite test failures"
                    return 0
                fi

                read -p "Continue with finalization anyway? (y/n): " continue_anyway
                if [[ "$continue_anyway" != "y" ]]; then
                    echo "⏸️ Finalization paused for test fixes"
                    exit 1
                fi
            fi
            break
        fi
    done

    if [[ "$script_found" == "false" ]]; then
        echo "⚠️ No acceptance tests found"
        echo "💡 Tried these locations:"
        printf '  - %s\n' "${acceptance_scripts[@]}"
        echo "💡 Consider creating acceptance tests for this milestone"
    fi
}

finalize_milestone() {
    echo ""
    echo "🏁 Finalizing Milestone $MILESTONE_ID"
    echo "====================================="

    # Update final state
    update_state "milestone_complete"

    # Add completion timestamp
    jq --arg timestamp "$(date -Iseconds)" \
       '.completed_at = $timestamp' \
       "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

    # Clean up state files if requested
    echo ""
    read -p "Clean up state files? (y/n): " cleanup_state

    if [[ "$cleanup_state" == "y" ]]; then
        cleanup_milestone_state
    fi

    echo ""
    echo "🎉 Milestone $MILESTONE_ID completed successfully!"
    echo ""
    echo "📋 Summary:"
    echo "  ✓ $TOTAL_TASKS tasks completed"
    echo "  ✓ Work logs updated"
    echo "  ✓ Code committed and merged"
    echo "  ✓ Acceptance tests run"
    echo ""
    echo "📁 Deliverables location: work-log/milestone-$MILESTONE_ID/"
    echo ""
    echo "🎯 Next steps:"
    echo "  - Review work logs and technical documentation"
    echo "  - Consider merging milestone branch to main"
    echo "  - Update project documentation"
    echo "  - Plan next milestone"
}

cleanup_milestone_state() {
    echo "🧹 Cleaning up milestone state..."

    # Archive state instead of deleting
    local archive_dir=".milestone-state/archived"
    mkdir -p "$archive_dir"

    if [[ -d "$STATE_DIR" ]]; then
        mv "$STATE_DIR" "$archive_dir/milestone-$MILESTONE_ID-$(date +%Y%m%d-%H%M%S)"
        echo "✅ State archived to: $archive_dir/"
    fi
}

# Recovery phase
phase_recovery() {
    echo ""
    echo "🚨 Recovery Mode"
    echo "==============="

    echo "Something seems to be wrong. Let me help you get back on track."
    echo ""

    # Diagnose current state
    diagnose_current_state

    echo ""
    echo "Recovery options:"
    echo "  1) Reset to start of current task ($CURRENT_TASK)"
    echo "  2) Reset to previous task ($((CURRENT_TASK - 1)))"
    echo "  3) Reset to milestone start"
    echo "  4) Manual recovery (get help)"
    echo ""

    read -p "Choose recovery option (1-4): " recovery_option

    case "$recovery_option" in
        1) reset_to_current_task ;;
        2) reset_to_previous_task ;;
        3) reset_to_milestone_start ;;
        4) show_manual_recovery_help ;;
        *)
            echo "Invalid option. Please choose 1-4."
            phase_recovery
            ;;
    esac
}

diagnose_current_state() {
    echo "🔍 Current state diagnosis:"
    echo "  Milestone: $MILESTONE_ID"
    echo "  Agent: $AGENT_TYPE"
    echo "  Phase: $CURRENT_PHASE"
    echo "  Task: $CURRENT_TASK of $TOTAL_TASKS"

    if command -v git >/dev/null && [[ -d ".git" ]]; then
        echo "  Git branch: $(git branch --show-current 2>/dev/null || echo 'unknown')"
        echo "  Git status: $(git status --porcelain | wc -l) modified files"
    fi

    echo "  State file: $STATE_FILE"
    echo "  Messages file: $MESSAGES_FILE"
    echo "  Instructions: $INSTRUCTIONS_FILE"
}

reset_to_current_task() {
    echo "🔄 Resetting to start of task $CURRENT_TASK..."

    # Reset git state if possible
    reset_git_to_task_start

    # Update state
    update_state "task_execution" "$CURRENT_TASK"

    echo "✅ Reset complete. Resuming normal execution..."
    phase_task_execution
}

reset_to_previous_task() {
    if [[ $CURRENT_TASK -le 1 ]]; then
        echo "❌ Already at first task. Cannot go to previous task."
        phase_recovery
        return
    fi

    local prev_task=$((CURRENT_TASK - 1))
    echo "🔄 Resetting to start of task $prev_task..."

    # Reset git state if possible
    reset_git_to_task_start "$prev_task"

    # Update state
    update_state "task_execution" "$prev_task"

    echo "✅ Reset complete. Resuming normal execution..."
    phase_task_execution
}

reset_to_milestone_start() {
    echo "🔄 Resetting to milestone start..."

    # Reset state completely
    update_state "not_started" 1

    echo "✅ Reset complete. Restarting milestone execution..."
    phase_pre_review
}

reset_git_to_task_start() {
    local task_num="${1:-$CURRENT_TASK}"

    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        echo "⚠️ Git not available. Skipping git reset."
        return
    fi

    local milestone_branch="milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    local task_branch="$milestone_branch/task-$(printf "%02d" $task_num)"

    echo "🔧 Resetting git state..."

    # Try to clean up current state
    git checkout "$milestone_branch" 2>/dev/null || echo "⚠️ Could not switch to milestone branch"

    # Delete task branch if it exists
    if git show-ref --verify --quiet "refs/heads/$task_branch"; then
        git branch -D "$task_branch" 2>/dev/null || echo "⚠️ Could not delete task branch"
        echo "✅ Cleaned up task branch: $task_branch"
    fi
}

show_manual_recovery_help() {
    echo ""
    echo "🆘 Manual Recovery Help"
    echo "======================"
    echo ""
    echo "If automatic recovery isn't working, you can:"
    echo ""
    echo "1. **Edit state file manually:**"
    echo "   File: $STATE_FILE"
    echo "   Change 'current_phase' and 'current_task' as needed"
    echo ""
    echo "2. **Send yourself a message:**"
    echo "   ./milestone-control.sh $MILESTONE_ID message \"Recovery guidance\""
    echo ""
    echo "3. **Check message history:**"
    echo "   ./milestone-control.sh $MILESTONE_ID messages"
    echo ""
    echo "4. **Reset git manually:**"
    echo "   git checkout milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    echo "   git branch -D <problematic-task-branch>"
    echo ""
    echo "5. **Ask for human help:**"
    echo ""

    ask_human_question "I'm stuck in recovery mode. Current state: Phase=$CURRENT_PHASE, Task=$CURRENT_TASK. What should I do?"

    echo ""
    echo "After manual fixes, run: $0 $MILESTONE_ID $AGENT_TYPE"
}

# Run main function
main "$@"
